"""
仪表盘API路由

提供仪表盘API接口，用于前端调用。
"""

from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from app.services.dashboard_service import DashboardService
from app.models_daily_management import DiningCompanion, DailyLog
from app.models import MenuPlan
from app import db
from sqlalchemy import desc, text
from datetime import date, datetime
from flask import current_app

# 创建蓝图
dashboard_api_bp = Blueprint('dashboard_api', __name__)

@dashboard_api_bp.route('/api/v2/dashboard/summary', methods=['GET'])
@login_required
def api_v2_dashboard_summary():
    """获取仪表盘摘要"""
    date_str = request.args.get('date')
    area_id = request.args.get('area_id', type=int)

    summary = DashboardService.get_dashboard_summary(date_str, area_id)
    return jsonify(summary)

@dashboard_api_bp.route('/api/v2/dashboard/weekly', methods=['GET'])
@login_required
def api_v2_weekly_summary():
    """获取周摘要"""
    week_start = request.args.get('week_start')
    area_id = request.args.get('area_id', type=int)

    summary = DashboardService.get_weekly_summary(week_start, area_id)
    return jsonify(summary)

@dashboard_api_bp.route('/api/v2/dashboard/monthly', methods=['GET'])
@login_required
def api_v2_monthly_summary():
    """获取月摘要"""
    year = request.args.get('year', type=int)
    month = request.args.get('month', type=int)
    area_id = request.args.get('area_id', type=int)

    summary = DashboardService.get_monthly_summary(year, month, area_id)
    return jsonify(summary)

@dashboard_api_bp.route('/api/v2/dining-companions/recent', methods=['GET'])
@login_required
def api_v2_recent_dining_companions():
    """获取最近的陪餐记录"""
    try:
        limit = request.args.get('limit', 5, type=int)
        current_app.logger.info(f"开始查询最近 {limit} 条陪餐记录")

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area:
            return jsonify([])

        # 使用 JOIN 查询优化性能，添加区域过滤
        companions = db.session.query(DiningCompanion, DailyLog)\
            .join(DailyLog, DiningCompanion.daily_log_id == DailyLog.id)\
            .filter(DailyLog.area_id == user_area.id)\
            .order_by(desc(DiningCompanion.dining_time))\
            .limit(limit)\
            .all()
        
        result = []
        for companion, log in companions:
            try:
                record = {
                    'id': companion.id,
                    'name': companion.companion_name or '匿名',
                    'role': companion.companion_role or '未知',
                    'time': companion.dining_time.strftime('%H:%M') if companion.dining_time else '',
                    'date': log.log_date.strftime('%Y-%m-%d') if log and log.log_date else '',
                    'meal_type': companion.meal_type or '',
                    'taste_rating': companion.taste_rating or 0,
                    'hygiene_rating': companion.hygiene_rating or 0,
                    'service_rating': companion.service_rating or 0
                }
                result.append(record)
            except Exception as e:
                current_app.logger.error(f"处理陪餐记录 {companion.id} 时出错: {str(e)}")
                continue
        
        current_app.logger.info(f"成功获取 {len(result)} 条陪餐记录")
        return jsonify(result)
        
    except Exception as e:
        current_app.logger.error(f"获取最近陪餐记录失败: {str(e)}")
        return jsonify({
            'error': '获取陪餐记录失败',
            'message': str(e)
        }), 500

@dashboard_api_bp.route('/api/v2/dashboard/today-menu', methods=['GET'])
@login_required
def api_v2_today_menu():
    """获取今日菜单"""
    try:
        today_date = date.today()

        menu_data = {
            '早餐': {'recipes': [], 'status': '暂无菜单'},
            '午餐': {'recipes': [], 'status': '暂无菜单'},
            '晚餐': {'recipes': [], 'status': '暂无菜单'}
        }

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area:
            return jsonify({'success': True, 'data': menu_data})

        # 查询今日菜单计划，添加区域过滤
        plans = MenuPlan.query.filter_by(
            plan_date=today_date,
            area_id=user_area.id
        ).all()

        for plan in plans:
            if plan.meal_type in menu_data:
                menu_data[plan.meal_type]['status'] = plan.status or '计划中'

                # 获取菜谱
                recipes = []
                for menu_recipe in plan.menu_recipes:
                    if menu_recipe.recipe:
                        recipes.append({
                            'name': menu_recipe.recipe.name,
                            'quantity': menu_recipe.planned_quantity
                        })

                menu_data[plan.meal_type]['recipes'] = recipes

        # 如果某个餐次没有日菜单，尝试从周菜单获取
        for meal_type in menu_data:
            if not menu_data[meal_type]['recipes']:  # 如果没有菜谱数据
                # 获取当天是周几（1-7，1表示周一）
                day_of_week = today_date.weekday() + 1

                # 查询当天的菜谱信息
                menu_sql = text("""
                SELECT wmr.recipe_name
                FROM weekly_menu_recipes wmr
                INNER JOIN weekly_menus wm ON wmr.weekly_menu_id = wm.id
                WHERE wmr.day_of_week = :day_of_week
                AND wmr.meal_type = :meal_type
                AND wm.area_id = :area_id
                AND wm.status IN ('已发布', '计划中')
                ORDER BY wmr.id
                """)

                menu_result = db.session.execute(menu_sql, {
                    'day_of_week': day_of_week,
                    'meal_type': meal_type,
                    'area_id': user_area.id
                })

                recipes = []
                for row in menu_result:
                    recipes.append({
                        'name': row.recipe_name,
                        'quantity': None
                    })

                if recipes:
                    menu_data[meal_type]['recipes'] = recipes
                    menu_data[meal_type]['status'] = '周菜单'

        return jsonify({
            'success': True,
            'data': menu_data,
            'date': today_date.strftime('%Y-%m-%d')
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


