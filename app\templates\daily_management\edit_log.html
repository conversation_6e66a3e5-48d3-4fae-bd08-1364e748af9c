{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    /* 简单的卡片式布局 */
    .quick-nav-card {
        border-left: 4px solid #4e73df;
        transition: all 0.3s;
    }

    .quick-nav-card:hover {
        transform: translateY(-2px);
    }

    .nav-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        color: white;
        margin: 0 auto 10px;
    }

    .icon-primary { background-color: #4e73df; }
    .icon-success { background-color: #1cc88a; }
    .icon-warning { background-color: #f6c23e; }
    .icon-info { background-color: #36b9cc; }
    .icon-danger { background-color: #e74a3b; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">
        {% if log %}编辑日志{% else %}创建日志{% endif %} - {{ log_date.strftime('%Y年%m月%d日') }}
    </h1>

    <!-- 快速导航 -->
    <div class="row mb-4">
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card quick-nav-card border-left-primary shadow h-100 py-2">
                <div class="card-body text-center">
                    <div class="nav-icon icon-primary">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                    {% if log %}
                    <a href="{{ url_for('daily_management.simplified_inspection', log_id=log.id) }}" class="btn btn-sm btn-primary">检查记录</a>
                    {% else %}
                    <span class="text-muted small">保存后可用</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card quick-nav-card border-left-success shadow h-100 py-2">
                <div class="card-body text-center">
                    <div class="nav-icon icon-success">
                        <i class="fas fa-user-friends"></i>
                    </div>
                    {% if log %}
                    <a href="{{ url_for('daily_management.companions', log_id=log.id) }}" class="btn btn-sm btn-success">陪餐记录</a>
                    {% else %}
                    <span class="text-muted small">保存后可用</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card quick-nav-card border-left-warning shadow h-100 py-2">
                <div class="card-body text-center">
                    <div class="nav-icon icon-warning">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    {% if log %}
                    <a href="{{ url_for('daily_management.trainings', log_id=log.id) }}" class="btn btn-sm btn-warning">培训记录</a>
                    {% else %}
                    <span class="text-muted small">保存后可用</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card quick-nav-card border-left-info shadow h-100 py-2">
                <div class="card-body text-center">
                    <div class="nav-icon icon-info">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    {% if log %}
                    <a href="{{ url_for('daily_management.events', log_id=log.id) }}" class="btn btn-sm btn-info">特殊事件</a>
                    {% else %}
                    <span class="text-muted small">保存后可用</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card quick-nav-card border-left-danger shadow h-100 py-2">
                <div class="card-body text-center">
                    <div class="nav-icon icon-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    {% if log %}
                    <a href="{{ url_for('daily_management.issues', log_id=log.id) }}" class="btn btn-sm btn-danger">问题记录</a>
                    {% else %}
                    <span class="text-muted small">保存后可用</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card quick-nav-card border-left-primary shadow h-100 py-2">
                <div class="card-body text-center">
                    <div class="nav-icon icon-primary">
                        <i class="fas fa-print"></i>
                    </div>
                    {% if log %}
                    <a href="{{ url_for('daily_management.print_log', log_id=log.id) }}" class="btn btn-sm btn-primary">打印日志</a>
                    {% else %}
                    <span class="text-muted small">保存后可用</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 表单区域 -->
    <form method="post" id="logForm">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

        <div class="row">
            <!-- 基本信息卡片 -->
            <div class="col-lg-6 mb-4">
                <div class="card form-card">
                    <div class="form-card-header">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-info-circle mr-2"></i>基本信息
                        </h6>
                    </div>
                    <div class="form-card-body">
                        <div class="form-group">
                            <label for="weather" class="font-weight-bold">
                                <i class="fas fa-cloud-sun mr-1 text-primary"></i>天气
                            </label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-cloud-sun"></i></span>
                                </div>
                                <input type="text" class="form-control" id="weather" name="weather"
                                       value="{{ log.weather if log else '' }}" placeholder="请输入当日天气">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="manager" class="font-weight-bold">
                                <i class="fas fa-user mr-1 text-success"></i>管理员
                            </label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                </div>
                                <input type="text" class="form-control" id="manager" name="manager"
                                       value="{{ log.manager if log else '' }}" placeholder="请输入管理员姓名">
                            </div>
                        </div>

                        <div class="form-group mb-0">
                            <label for="food_waste" class="font-weight-bold">
                                <i class="fas fa-trash mr-1 text-warning"></i>食物浪费量(kg)
                            </label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-trash"></i></span>
                                </div>
                                <input type="number" step="0.01" class="form-control" id="food_waste" name="food_waste"
                                       value="{{ log.food_waste if log else 0 }}" min="0">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 就餐统计卡片 -->
            <div class="col-lg-6 mb-4">
                <div class="card form-card">
                    <div class="form-card-header">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-users mr-2"></i>就餐人数统计
                        </h6>
                    </div>
                    <div class="form-card-body">
                        <div class="form-group">
                            <label for="student_count" class="font-weight-bold">
                                <i class="fas fa-user-graduate mr-1 text-primary"></i>学生就餐人数
                            </label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-user-graduate"></i></span>
                                </div>
                                <input type="number" class="form-control" id="student_count" name="student_count"
                                       value="{{ log.student_count if log else 0 }}" min="0">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="teacher_count" class="font-weight-bold">
                                <i class="fas fa-chalkboard-teacher mr-1 text-success"></i>教师就餐人数
                            </label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-chalkboard-teacher"></i></span>
                                </div>
                                <input type="number" class="form-control" id="teacher_count" name="teacher_count"
                                       value="{{ log.teacher_count if log else 0 }}" min="0">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="other_count" class="font-weight-bold">
                                <i class="fas fa-user-friends mr-1 text-info"></i>其他就餐人数
                            </label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-user-friends"></i></span>
                                </div>
                                <input type="number" class="form-control" id="other_count" name="other_count"
                                       value="{{ log.other_count if log else 0 }}" min="0">
                            </div>
                        </div>

                        <!-- 总人数统计 -->
                        <div class="stats-card">
                            <h4 class="mb-1">
                                <span id="total-count">{{ (log.student_count or 0) + (log.teacher_count or 0) + (log.other_count or 0) }}</span>
                            </h4>
                            <p class="mb-0">总就餐人数</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 菜单信息卡片 -->
        <div class="row">
            {% if menu_data %}
            <!-- 当日菜谱预览 -->
            <div class="col-lg-4 mb-4">
                <div class="card form-card">
                    <div class="form-card-header">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-utensils mr-2"></i>当日菜谱预览
                        </h6>
                    </div>
                    <div class="form-card-body">
                        <!-- 早餐 -->
                        <div class="menu-preview-card mb-3">
                            <h6 class="mb-2">
                                <i class="fas fa-sun mr-1"></i>早餐
                            </h6>
                            <div>
                                {% if menu_data.breakfast %}
                                    {% for item in menu_data.breakfast %}
                                    <span class="menu-item-tag menu-item" data-meal="breakfast">{{ item }}</span>
                                    {% endfor %}
                                {% else %}
                                    <span class="text-white-50">暂无早餐菜谱</span>
                                {% endif %}
                            </div>
                        </div>

                        <!-- 午餐 -->
                        <div class="menu-preview-card mb-3">
                            <h6 class="mb-2">
                                <i class="fas fa-utensils mr-1"></i>午餐
                            </h6>
                            <div>
                                {% if menu_data.lunch %}
                                    {% for item in menu_data.lunch %}
                                    <span class="menu-item-tag menu-item" data-meal="lunch">{{ item }}</span>
                                    {% endfor %}
                                {% else %}
                                    <span class="text-white-50">暂无午餐菜谱</span>
                                {% endif %}
                            </div>
                        </div>

                        <!-- 晚餐 -->
                        <div class="menu-preview-card">
                            <h6 class="mb-2">
                                <i class="fas fa-moon mr-1"></i>晚餐
                            </h6>
                            <div>
                                {% if menu_data.dinner %}
                                    {% for item in menu_data.dinner %}
                                    <span class="menu-item-tag menu-item" data-meal="dinner">{{ item }}</span>
                                    {% endfor %}
                                {% else %}
                                    <span class="text-white-50">暂无晚餐菜谱</span>
                                {% endif %}
                            </div>
                        </div>

                        <div class="text-center mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle mr-1"></i>点击菜品可快速添加到菜单
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- 菜单编辑 -->
            <div class="col-lg-{% if menu_data %}8{% else %}12{% endif %} mb-4">
                <div class="card form-card">
                    <div class="form-card-header">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-edit mr-2"></i>菜单编辑
                        </h6>
                    </div>
                    <div class="form-card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="breakfast_menu" class="font-weight-bold">
                                        <i class="fas fa-sun mr-1 text-warning"></i>早餐菜单
                                    </label>
                                    <textarea class="form-control" id="breakfast_menu" name="breakfast_menu"
                                              rows="4" placeholder="请输入早餐菜单...">{{ log.breakfast_menu if log else (', '.join(menu_data.breakfast) if menu_data and menu_data.breakfast else '') }}</textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="lunch_menu" class="font-weight-bold">
                                        <i class="fas fa-utensils mr-1 text-primary"></i>午餐菜单
                                    </label>
                                    <textarea class="form-control" id="lunch_menu" name="lunch_menu"
                                              rows="4" placeholder="请输入午餐菜单...">{{ log.lunch_menu if log else (', '.join(menu_data.lunch) if menu_data and menu_data.lunch else '') }}</textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="dinner_menu" class="font-weight-bold">
                                        <i class="fas fa-moon mr-1 text-info"></i>晚餐菜单
                                    </label>
                                    <textarea class="form-control" id="dinner_menu" name="dinner_menu"
                                              rows="4" placeholder="请输入晚餐菜单...">{{ log.dinner_menu if log else (', '.join(menu_data.dinner) if menu_data and menu_data.dinner else '') }}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 其他信息卡片 -->
        <div class="row">
            <div class="col-lg-12 mb-4">
                <div class="card form-card">
                    <div class="form-card-header">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-clipboard-list mr-2"></i>其他信息
                        </h6>
                    </div>
                    <div class="form-card-body">
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <div class="form-group">
                                    <label for="special_events" class="font-weight-bold">
                                        <i class="fas fa-exclamation-circle mr-1 text-warning"></i>特殊事件概述
                                    </label>
                                    <textarea class="form-control" id="special_events" name="special_events"
                                              rows="3" placeholder="请简要描述当日特殊事件...">{{ log.special_events if log else '' }}</textarea>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="form-group mb-0">
                                    <label for="operation_summary" class="font-weight-bold">
                                        <i class="fas fa-chart-line mr-1 text-success"></i>运营总结
                                    </label>
                                    <textarea class="form-control" id="operation_summary" name="operation_summary"
                                              rows="4" placeholder="请输入当日运营总结...">{{ log.operation_summary if log else '' }}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-4">
                    <button type="submit" class="action-btn btn-gradient-primary mr-3">
                        <i class="fas fa-save mr-2"></i>保存日志
                    </button>
                    <a href="{{ url_for('daily_management.logs') }}" class="action-btn btn-gradient-success">
                        <i class="fas fa-list mr-2"></i>返回列表
                    </a>
                    <a href="{{ url_for('daily_management.index') }}" class="action-btn btn btn-outline-secondary ml-3">
                        <i class="fas fa-home mr-2"></i>返回首页
                    </a>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/datatables/js/jquery.dataTables.min.js') }}"></script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/datatables/js/dataTables.bootstrap4.min.js') }}"></script>
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 计算总就餐人数
        function calculateTotal() {
            var studentCount = parseInt($('#student_count').val()) || 0;
            var teacherCount = parseInt($('#teacher_count').val()) || 0;
            var otherCount = parseInt($('#other_count').val()) || 0;
            var total = studentCount + teacherCount + otherCount;
            $('#total-count').text(total);
        }

        // 监听就餐人数输入框的变化
        $('#student_count, #teacher_count, #other_count').on('input', calculateTotal);

        // 菜谱自动填充功能
        {% if menu_data %}
        // 如果菜单输入框为空且有菜谱数据，则自动填充
        {% if menu_data.breakfast and not (log and log.breakfast_menu) %}
        if (!$('#breakfast_menu').val().trim()) {
            $('#breakfast_menu').val('{{ ", ".join(menu_data.breakfast) }}');
        }
        {% endif %}

        {% if menu_data.lunch and not (log and log.lunch_menu) %}
        if (!$('#lunch_menu').val().trim()) {
            $('#lunch_menu').val('{{ ", ".join(menu_data.lunch) }}');
        }
        {% endif %}

        {% if menu_data.dinner and not (log and log.dinner_menu) %}
        if (!$('#dinner_menu').val().trim()) {
            $('#dinner_menu').val('{{ ", ".join(menu_data.dinner) }}');
        }
        {% endif %}
        {% endif %}

        // 添加菜谱项目到输入框的功能
        $('.menu-item').on('click', function() {
            var menuText = $(this).text();
            var mealType = '';

            // 确定餐次类型
            if ($(this).closest('.col-md-4').index() === 0) {
                mealType = 'breakfast';
            } else if ($(this).closest('.col-md-4').index() === 1) {
                mealType = 'lunch';
            } else {
                mealType = 'dinner';
            }

            var textarea = $('#' + mealType + '_menu');
            var currentValue = textarea.val();

            // 如果当前值为空，直接设置
            if (!currentValue.trim()) {
                textarea.val(menuText);
            } else {
                // 如果已有内容，检查是否已包含该项目
                if (currentValue.indexOf(menuText) === -1) {
                    textarea.val(currentValue + ', ' + menuText);
                }
            }

            // 调整文本区域高度
            textarea.trigger('input');

            // 高亮显示点击的项目
            $(this).addClass('bg-primary text-white').delay(300).queue(function() {
                $(this).removeClass('bg-primary text-white').dequeue();
            });
        });

        // 表单验证
        $('#logForm').on('submit', function(e) {
            // 显示加载状态
            $('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 保存中...');
        });

        // 初始化提示工具
        $('[data-toggle="tooltip"]').tooltip();

        // 自动调整文本区域高度
        $('textarea').each(function() {
            this.setAttribute('style', 'height:' + (this.scrollHeight) + 'px;overflow-y:hidden;');
        }).on('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });

        // 菜谱项目悬停效果
        $('.menu-item').hover(
            function() {
                $(this).addClass('shadow-sm').css('cursor', 'pointer');
            },
            function() {
                $(this).removeClass('shadow-sm');
            }
        );

        // 添加菜谱提示
        $('.menu-item').attr('title', '点击添加到菜单输入框');
        $('[title]').tooltip();
    });
</script>
{% endblock %}
