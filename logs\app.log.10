2025-05-31 22:44:41,957 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 22:44:43,257 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 22:44:51,341 INFO: 开始删除操作 - 实体类型: AdministrativeArea, ID: 41, 强制删除: True, 演习模式: True [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:193]
2025-05-31 22:44:51,343 INFO: 找到实体: AdministrativeArea (ID: 41) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:217]
2025-05-31 22:44:51,343 INFO: 开始检查依赖关系 - AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:231]
2025-05-31 22:44:51,343 INFO: 检查依赖: audit_logs -> AuditLog (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:44:51,343 INFO: 找到 0 个依赖实体: AuditLog [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:44:51,343 INFO: 检查依赖: children -> AdministrativeArea (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:44:51,344 INFO: 找到 0 个依赖实体: AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:44:51,344 INFO: 检查依赖: users -> User (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:44:51,344 INFO: 找到 1 个依赖实体: User [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:44:51,344 INFO: 检查依赖: menu_plans -> MenuPlan (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:44:51,345 INFO: 找到 0 个依赖实体: MenuPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:44:51,345 INFO: 检查依赖: consumption_plans -> ConsumptionPlan (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:44:51,345 INFO: 找到 0 个依赖实体: ConsumptionPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:44:51,345 INFO: 检查依赖: purchase_orders -> PurchaseOrder (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:44:51,345 INFO: 找到 3 个依赖实体: PurchaseOrder [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:44:51,346 INFO: 检查依赖: warehouses -> Warehouse (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:44:51,346 INFO: 找到 0 个依赖实体: Warehouse [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:44:51,346 INFO: 检查依赖: supplier_relations -> SupplierSchoolRelation (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:44:51,346 INFO: 找到 0 个依赖实体: SupplierSchoolRelation [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:44:51,346 INFO: 演习模式：模拟删除 AdministrativeArea (ID: 41) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:277]
2025-05-31 22:44:51,347 INFO: 演习模式：找到 0 个依赖实体: AuditLog [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 22:44:51,348 INFO: 演习模式：找到 0 个依赖实体: AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 22:44:51,348 INFO: 演习模式：递归模拟删除子区域 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:307]
2025-05-31 22:44:51,350 INFO: 演习模式：找到 1 个依赖实体: User [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 22:44:51,353 INFO: 演习模式：找到 0 个依赖实体: MenuPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 22:44:51,356 INFO: 演习模式：找到 0 个依赖实体: ConsumptionPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 22:44:51,358 INFO: 演习模式：找到 3 个依赖实体: PurchaseOrder [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 22:44:51,359 INFO: 演习模式：找到 0 个依赖实体: Warehouse [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 22:44:51,360 INFO: 演习模式：找到 0 个依赖实体: SupplierSchoolRelation [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:298]
2025-05-31 22:44:55,775 INFO: 开始删除操作 - 实体类型: AdministrativeArea, ID: 41, 强制删除: True, 演习模式: False [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:193]
2025-05-31 22:44:55,776 INFO: 找到实体: AdministrativeArea (ID: 41) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:217]
2025-05-31 22:44:55,777 INFO: 开始检查依赖关系 - AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:231]
2025-05-31 22:44:55,777 INFO: 检查依赖: audit_logs -> AuditLog (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:44:55,778 INFO: 找到 0 个依赖实体: AuditLog [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:44:55,778 INFO: 检查依赖: children -> AdministrativeArea (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:44:55,778 INFO: 找到 0 个依赖实体: AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:44:55,779 INFO: 检查依赖: users -> User (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:44:55,779 INFO: 找到 1 个依赖实体: User [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:44:55,779 INFO: 检查依赖: menu_plans -> MenuPlan (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:44:55,779 INFO: 找到 0 个依赖实体: MenuPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:44:55,780 INFO: 检查依赖: consumption_plans -> ConsumptionPlan (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:44:55,782 INFO: 找到 0 个依赖实体: ConsumptionPlan [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:44:55,782 INFO: 检查依赖: purchase_orders -> PurchaseOrder (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:44:55,783 INFO: 找到 3 个依赖实体: PurchaseOrder [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:44:55,783 INFO: 检查依赖: warehouses -> Warehouse (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:44:55,785 INFO: 找到 0 个依赖实体: Warehouse [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:44:55,786 INFO: 检查依赖: supplier_relations -> SupplierSchoolRelation (级联: True, 必需: True) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:238]
2025-05-31 22:44:55,787 INFO: 找到 0 个依赖实体: SupplierSchoolRelation [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:256]
2025-05-31 22:44:55,788 INFO: 开始处理依赖关系 - AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:341]
2025-05-31 22:44:55,788 INFO: 处理级联删除: audit_logs -> AuditLog [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:349]
2025-05-31 22:44:55,790 INFO: 使用SQL查询到 0 个依赖实体ID: AuditLog [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:360]
2025-05-31 22:44:55,791 INFO: 准备删除 0 个依赖实体: AuditLog [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:367]
2025-05-31 22:44:55,791 INFO: 处理级联删除: children -> AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:349]
2025-05-31 22:44:55,798 INFO: 准备删除 0 个依赖实体: AdministrativeArea [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\super_delete.py:367]
