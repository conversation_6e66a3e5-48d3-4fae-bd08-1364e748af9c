from flask import render_template, redirect, url_for, flash, request, current_app, abort, jsonify, send_file, make_response
from flask_wtf import FlaskForm
from flask_login import login_required, current_user
from app import db
from app.routes.daily_management import daily_management_bp
from app.models_daily_management import (
    DailyLog, InspectionRecord, DiningCompanion,
    CanteenTrainingRecord, SpecialEvent, Issue, Photo,
    InspectionTemplate
)
from app.models import AdministrativeArea
from datetime import datetime, date, timedelta
from sqlalchemy import text
import os
from werkzeug.utils import secure_filename
from PIL import Image
from app.utils.qrcode_helper import generate_companion_qrcode, generate_qrcode_base64

# 首页 - 日常管理模块主页
@daily_management_bp.route('/')
@login_required
def index():
    today = date.today()

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法访问食堂日常管理模块', 'danger')
        return redirect(url_for('main.index'))

    # 获取今日日志 - 限制为用户所属学校
    today_log = DailyLog.query.filter_by(log_date=today, area_id=user_area.id).first()

    # 获取最近的日志 - 限制为用户所属学校
    recent_logs = DailyLog.query.filter_by(area_id=user_area.id).order_by(DailyLog.log_date.desc()).limit(5).all()

    # 获取待处理问题 - 限制为用户所属学校的日志关联的问题
    if recent_logs:
        log_ids = [log.id for log in recent_logs]
        pending_issues = Issue.query.filter(
            Issue.daily_log_id.in_(log_ids),
            Issue.status == 'pending'
        ).order_by(Issue.found_time.desc()).limit(5).all()
    else:
        pending_issues = []

    # 获取检查记录数量
    inspection_count = 0
    companion_count = 0
    if today_log:
        inspection_count = InspectionRecord.query.filter_by(daily_log_id=today_log.id).count()
        companion_count = DiningCompanion.query.filter_by(daily_log_id=today_log.id).count()

        # 为最近日志添加统计数据
        for log in recent_logs:
            log.inspection_count = InspectionRecord.query.filter_by(daily_log_id=log.id).count()
            log.companion_count = DiningCompanion.query.filter_by(daily_log_id=log.id).count()
            log.issue_count = Issue.query.filter(
                Issue.daily_log_id == log.id
            ).count()

    # 使用简化版仪表盘模板
    return render_template('daily_management/simplified_dashboard.html',
                          title=f'{user_area.name} - 食堂日常管理仪表盘',
                          today_log=today_log,
                          recent_logs=recent_logs,
                          pending_issues=pending_issues,
                          inspection_count=inspection_count,
                          companion_count=companion_count,
                          today=today,
                          school=user_area)

# 日常管理中心路由已删除

# 旧版首页 - 用于兼容性
@daily_management_bp.route('/old-index')
@login_required
def old_index():
    today = date.today()
    # 获取今日日志
    today_log = DailyLog.query.filter_by(log_date=today).first()

    # 获取最近的日志
    recent_logs = DailyLog.query.order_by(DailyLog.log_date.desc()).limit(5).all()

    # 获取待处理问题
    pending_issues = Issue.query.filter_by(status='pending').order_by(Issue.found_time.desc()).limit(5).all()

    return render_template('daily_management/index.html',
                          title='食堂日常管理',
                          today_log=today_log,
                          recent_logs=recent_logs,
                          pending_issues=pending_issues,
                          today=today)

# 自动创建日志并跳转到检查记录页面
@daily_management_bp.route('/auto-inspections')
@login_required
def auto_inspections():
    """自动创建日志并跳转到检查记录页面"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法创建日志', 'danger')
        return redirect(url_for('daily_management.index'))

    today_date = date.today()

    try:
        # 使用 DailyLogService 创建日志，如果已存在则返回已存在的日志
        from app.services.daily_management_service import DailyLogService

        # 准备数据
        data = {
            'log_date': today_date.strftime('%Y-%m-%d'),  # 转换为字符串格式
            'area_id': user_area.id,
            'manager': current_user.real_name or current_user.username,
            'student_count': 0,
            'teacher_count': 0,
            'other_count': 0,
            'created_by': current_user.id
        }

        # 创建或获取日志
        log = DailyLogService.create_daily_log(data)

        if log:
            flash(f'已自动创建或获取{user_area.name}今日工作日志', 'info')
        else:
            flash(f'无法创建或获取日志', 'danger')
            return redirect(url_for('daily_management.index'))

    except Exception as e:
        current_app.logger.error(f"创建日志失败: {str(e)}")
        flash(f'创建日志失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.index'))

    return redirect(url_for('daily_management.simplified_inspection', log_id=log.id))

# 自动创建日志并跳转到陪餐记录页面
@daily_management_bp.route('/auto-companions')
@login_required
def auto_companions():
    """自动创建日志并跳转到陪餐记录页面"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法创建日志', 'danger')
        return redirect(url_for('daily_management.index'))

    today_date = date.today()

    try:
        # 使用 DailyLogService 创建日志，如果已存在则返回已存在的日志
        from app.services.daily_management_service import DailyLogService

        # 准备数据
        data = {
            'log_date': today_date.strftime('%Y-%m-%d'),  # 转换为字符串格式
            'area_id': user_area.id,
            'manager': current_user.real_name or current_user.username,
            'student_count': 0,
            'teacher_count': 0,
            'other_count': 0,
            'created_by': current_user.id
        }

        # 创建或获取日志
        log = DailyLogService.create_daily_log(data)

        if log:
            flash(f'已自动创建或获取{user_area.name}今日工作日志', 'info')
        else:
            flash(f'无法创建或获取日志', 'danger')
            return redirect(url_for('daily_management.index'))

    except Exception as e:
        current_app.logger.error(f"创建日志失败: {str(e)}")
        flash(f'创建日志失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.index'))

    return redirect(url_for('daily_management.companions', log_id=log.id))

# 自动创建日志并跳转到培训记录页面
@daily_management_bp.route('/auto-trainings')
@login_required
def auto_trainings():
    """自动创建日志并跳转到培训记录页面"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法创建日志', 'danger')
        return redirect(url_for('daily_management.index'))

    today_date = date.today()

    try:
        # 使用 DailyLogService 创建日志，如果已存在则返回已存在的日志
        from app.services.daily_management_service import DailyLogService

        # 准备数据
        data = {
            'log_date': today_date.strftime('%Y-%m-%d'),  # 转换为字符串格式
            'area_id': user_area.id,
            'manager': current_user.real_name or current_user.username,
            'student_count': 0,
            'teacher_count': 0,
            'other_count': 0,
            'created_by': current_user.id
        }

        # 创建或获取日志
        log = DailyLogService.create_daily_log(data)

        if log:
            flash(f'已自动创建或获取{user_area.name}今日工作日志', 'info')
        else:
            flash(f'无法创建或获取日志', 'danger')
            return redirect(url_for('daily_management.index'))

    except Exception as e:
        current_app.logger.error(f"创建日志失败: {str(e)}")
        flash(f'创建日志失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.index'))

    return redirect(url_for('daily_management.trainings', log_id=log.id))

# 自动创建日志并跳转到特殊事件页面
@daily_management_bp.route('/auto-events')
@login_required
def auto_events():
    """自动创建日志并跳转到特殊事件页面"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法创建日志', 'danger')
        return redirect(url_for('daily_management.index'))

    today_date = date.today()

    try:
        # 使用 DailyLogService 创建日志，如果已存在则返回已存在的日志
        from app.services.daily_management_service import DailyLogService

        # 准备数据
        data = {
            'log_date': today_date.strftime('%Y-%m-%d'),  # 转换为字符串格式
            'area_id': user_area.id,
            'manager': current_user.real_name or current_user.username,
            'student_count': 0,
            'teacher_count': 0,
            'other_count': 0,
            'created_by': current_user.id
        }

        # 创建或获取日志
        log = DailyLogService.create_daily_log(data)

        if log:
            flash(f'已自动创建或获取{user_area.name}今日工作日志', 'info')
        else:
            flash(f'无法创建或获取日志', 'danger')
            return redirect(url_for('daily_management.index'))

    except Exception as e:
        current_app.logger.error(f"创建日志失败: {str(e)}")
        flash(f'创建日志失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.index'))

    return redirect(url_for('daily_management.events', log_id=log.id))

# 自动创建日志并跳转到问题记录页面
@daily_management_bp.route('/auto-issues')
@login_required
def auto_issues():
    """自动创建日志并跳转到问题记录页面"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法创建日志', 'danger')
        return redirect(url_for('daily_management.index'))

    today_date = date.today()

    try:
        # 使用 DailyLogService 创建日志，如果已存在则返回已存在的日志
        from app.services.daily_management_service import DailyLogService

        # 准备数据
        data = {
            'log_date': today_date.strftime('%Y-%m-%d'),  # 转换为字符串格式
            'area_id': user_area.id,
            'manager': current_user.real_name or current_user.username,
            'student_count': 0,
            'teacher_count': 0,
            'other_count': 0,
            'created_by': current_user.id
        }

        # 创建或获取日志
        log = DailyLogService.create_daily_log(data)

        if log:
            flash(f'已自动创建或获取{user_area.name}今日工作日志', 'info')
        else:
            flash(f'无法创建或获取日志', 'danger')
            return redirect(url_for('daily_management.index'))

    except Exception as e:
        current_app.logger.error(f"创建日志失败: {str(e)}")
        flash(f'创建日志失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.index'))

    return redirect(url_for('daily_management.issues', log_id=log.id))

# 日志管理 - 重定向到当前日期的日志编辑页面
@daily_management_bp.route('/logs')
@login_required
def logs():
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法查看日志', 'danger')
        return redirect(url_for('main.index'))

    # 获取当前日期并重定向到编辑页面
    today = datetime.now().date()
    today_str = today.strftime('%Y-%m-%d')

    return redirect(url_for('daily_management.edit_log', date_str=today_str))

# 创建/编辑日志
@daily_management_bp.route('/logs/edit/<date_str>', methods=['GET', 'POST'])
@login_required
def edit_log(date_str):
    # 导入 DailyLogService
    from app.services.daily_management_service import DailyLogService

    # 获取用户所属学校
    user_area = current_user.get_current_area()

    try:
        # 处理日期时间参数
        try:
            log_date = datetime.strptime(date_str, '%Y-%m-%d').replace(microsecond=0)
        except ValueError:
            flash('无效的日期格式', 'danger')
            return redirect(url_for('daily_management.logs'))

        # 获取日志
        log = DailyLogService.get_daily_log_by_date(log_date, user_area.id)

        # 直接调用 DailyLogService 的菜单获取方法
        menu_data = DailyLogService.get_menu_data_for_date(log_date.date(), user_area.id)

        if request.method == 'POST':
            if log is None:
                # 创建新日志
                data = {
                    'log_date': log_date,  # 直接传递 datetime 对象
                    'weather': request.form.get('weather') or None,
                    'manager': request.form.get('manager') or None,
                    'student_count': request.form.get('student_count', type=int) or 0,
                    'teacher_count': request.form.get('teacher_count', type=int) or 0,
                    'other_count': request.form.get('other_count', type=int) or 0,
                    'breakfast_menu': request.form.get('breakfast_menu') or None,
                    'lunch_menu': request.form.get('lunch_menu') or None,
                    'dinner_menu': request.form.get('dinner_menu') or None,
                    'food_waste': request.form.get('food_waste', type=float) or 0.0,
                    'special_events': request.form.get('special_events') or None,
                    'operation_summary': request.form.get('operation_summary') or None,
                    'area_id': user_area.id,
                    'created_by': current_user.id
                }

                # 创建日志
                log = DailyLogService.create_daily_log(data)
                if not log:
                    flash('创建日志失败，可能已存在相同日期的日志', 'danger')
                    return redirect(url_for('daily_management.logs'))
            else:
                # 更新现有日志
                data = {
                    'weather': request.form.get('weather') or None,
                    'manager': request.form.get('manager') or None,
                    'student_count': request.form.get('student_count', type=int) or 0,
                    'teacher_count': request.form.get('teacher_count', type=int) or 0,
                    'other_count': request.form.get('other_count', type=int) or 0,
                    'breakfast_menu': request.form.get('breakfast_menu') or None,
                    'lunch_menu': request.form.get('lunch_menu') or None,
                    'dinner_menu': request.form.get('dinner_menu') or None,
                    'food_waste': request.form.get('food_waste', type=float) or 0.0,
                    'special_events': request.form.get('special_events') or None,
                    'operation_summary': request.form.get('operation_summary') or None
                }

                # 更新日志
                log = DailyLogService.update_daily_log(log.id, data)
                if not log:
                    flash('更新日志失败，日志可能已被删除', 'danger')
                    return redirect(url_for('daily_management.logs'))

            flash('日志保存成功', 'success')
            return redirect(url_for('daily_management.logs'))

        # GET 请求，渲染编辑表单
        return render_template('daily_management/edit_log.html',
                              title=f'{user_area.name} - 编辑日志',
                              log=log,
                              log_date=log_date,
                              menu_data=menu_data,
                              now=datetime.now(),
                              school=user_area)

    except Exception as e:
        flash(f'保存失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.logs'))

# 打印日志
@daily_management_bp.route('/print-log/<int:log_id>')
@login_required
def print_log(log_id):
    """打印日志及其所有相关记录"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法打印日志', 'danger')
        return redirect(url_for('main.index'))

    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 确保用户只能打印自己学校的日志
    if log.area_id != user_area.id:
        flash('您没有权限打印该日志', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取检查记录
    morning_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='morning').all()
    noon_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='noon').all()
    evening_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='evening').all()

    # 获取陪餐记录
    companions = DiningCompanion.query.filter_by(daily_log_id=log_id).order_by(DiningCompanion.dining_time).all()

    # 获取培训记录
    trainings = CanteenTrainingRecord.query.filter_by(daily_log_id=log_id).order_by(CanteenTrainingRecord.training_time).all()

    # 获取特殊事件
    events = SpecialEvent.query.filter_by(daily_log_id=log_id).order_by(SpecialEvent.event_time).all()

    # 获取问题记录
    issues = Issue.query.filter_by(daily_log_id=log_id).order_by(Issue.found_time).all()

    # 渲染打印模板
    return render_template('daily_management/print_log.html',
                          title=f'{user_area.name} - 食堂日常管理日志打印',
                          log=log,
                          morning_inspections=morning_inspections,
                          noon_inspections=noon_inspections,
                          evening_inspections=evening_inspections,
                          companions=companions,
                          trainings=trainings,
                          events=events,
                          issues=issues,
                          school=user_area,
                          now=datetime.now())

# 检查模板管理
@daily_management_bp.route('/inspection-templates')
@login_required
def inspection_templates():
    """检查模板管理页面"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法访问检查模板管理', 'danger')
        return redirect(url_for('main.index'))

    # 获取URL参数
    log_id = request.args.get('log_id', type=int)
    inspection_type = request.args.get('inspection_type')

    return render_template('daily_management/inspection_templates.html',
                          title=f'{user_area.name} - 检查模板管理',
                          school=user_area,
                          log_id=log_id,
                          inspection_type=inspection_type)

# 打印检查记录
@daily_management_bp.route('/print-inspections/<int:log_id>')
@login_required
def print_inspections(log_id):
    """打印检查记录"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法打印检查记录', 'danger')
        return redirect(url_for('main.index'))

    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 确保用户只能打印自己学校的日志
    if log.area_id != user_area.id:
        flash('您没有权限打印该检查记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取检查记录
    morning_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='morning').all()
    noon_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='noon').all()
    evening_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='evening').all()

    # 渲染打印模板
    return render_template('daily_management/print/print_inspections.html',
                          title=f'{user_area.name} - 检查记录打印',
                          log=log,
                          morning_inspections=morning_inspections,
                          noon_inspections=noon_inspections,
                          evening_inspections=evening_inspections,
                          school=user_area)




# 打印特殊事件
@daily_management_bp.route('/print-events/<int:log_id>')
@login_required
def print_events(log_id):
    """打印特殊事件"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法打印特殊事件', 'danger')
        return redirect(url_for('main.index'))

    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 确保用户只能打印自己学校的日志
    if log.area_id != user_area.id:
        flash('您没有权限打印该特殊事件', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取特殊事件
    events = SpecialEvent.query.filter_by(daily_log_id=log_id).order_by(SpecialEvent.event_time).all()

    # 渲染打印模板
    return render_template('daily_management/print/print_events.html',
                          title=f'{user_area.name} - 特殊事件打印',
                          log=log,
                          events=events,
                          school=user_area)

# 打印问题记录
@daily_management_bp.route('/print-issues/<int:log_id>')
@login_required
def print_issues(log_id):
    """打印问题记录"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法打印问题记录', 'danger')
        return redirect(url_for('main.index'))

    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 确保用户只能打印自己学校的日志
    if log.area_id != user_area.id:
        flash('您没有权限打印该问题记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取问题记录
    issues = Issue.query.filter_by(daily_log_id=log_id).order_by(Issue.found_time).all()

    # 渲染打印模板
    return render_template('daily_management/print/print_issues.html',
                          title=f'{user_area.name} - 问题记录打印',
                          log=log,
                          issues=issues,
                          school=user_area)

# 检查记录 - 重定向到简化页面
@daily_management_bp.route('/inspections/<int:log_id>')
@login_required
def inspections(log_id):
    """检查记录页面 - 重定向到简化检查记录页面

    参数:
        log_id: 日志ID

    返回:
        重定向到简化检查记录页面
    """
    # 重定向到简化检查记录页面
    return redirect(url_for('daily_management.simplified_inspection', log_id=log_id))

# 创建/编辑检查记录 - 重定向到简化页面
@daily_management_bp.route('/inspections/edit/<int:log_id>/<inspection_type>', methods=['GET', 'POST'])
@login_required
def edit_inspection(log_id, inspection_type):
    """编辑检查记录 - 重定向到简化检查记录页面

    参数:
        log_id: 日志ID
        inspection_type: 检查类型

    返回:
        重定向到简化检查记录页面
    """
    # 重定向到简化检查记录页面
    return redirect(url_for('daily_management.simplified_inspection', log_id=log_id))

# 陪餐记录
@daily_management_bp.route('/companions/<int:log_id>')
@login_required
def companions(log_id):
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法查看陪餐记录', 'danger')
        return redirect(url_for('main.index'))

    # 使用原始 SQL 查询获取日志，并确保属于用户所属学校
    sql = text("""
    SELECT * FROM daily_logs WHERE id = :log_id AND area_id = :area_id
    """)
    result = db.session.execute(sql, {'log_id': log_id, 'area_id': user_area.id})
    log = result.fetchone()

    if not log:
        flash('您没有权限查看该日志的陪餐记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 使用原始 SQL 查询获取陪餐记录
    sql = text("""
    SELECT * FROM dining_companions
    WHERE daily_log_id = :log_id
    ORDER BY dining_time
    """)
    result = db.session.execute(sql, {'log_id': log_id})
    companions = result.fetchall()

    # 创建空表单对象，用于生成 CSRF 令牌
    form = FlaskForm()

    return render_template('daily_management/companions.html',
                          title=f'{user_area.name} - 陪餐记录',
                          log=log,
                          companions=companions,
                          form=form,
                          school=user_area)

# 创建陪餐记录
@daily_management_bp.route('/companions/add/<int:log_id>', methods=['GET', 'POST'])
@login_required
def add_companion(log_id):
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法添加陪餐记录', 'danger')
        return redirect(url_for('main.index'))

    # 确保日志属于用户所属学校
    log = DailyLog.query.filter_by(id=log_id, area_id=user_area.id).first_or_404()

    # 检查是否是从日常管理中心页面加载
    is_iframe = request.args.get('iframe', '0') == '1'

    if request.method == 'POST':
        try:
            # 使用原始 SQL 语句创建陪餐记录
            sql = text("""
            INSERT INTO dining_companions
            (daily_log_id, companion_name, companion_role, meal_type, dining_time,
            taste_rating, hygiene_rating, service_rating, comments, suggestions)
            OUTPUT inserted.id
            VALUES
            (:daily_log_id, :companion_name, :companion_role, :meal_type, :dining_time,
            :taste_rating, :hygiene_rating, :service_rating, :comments, :suggestions)
            """)

            # 准备参数
            dining_time = datetime.strptime(f"{request.form.get('dining_date')} {request.form.get('dining_time')}", '%Y-%m-%d %H:%M')
            params = {
                'daily_log_id': log_id,
                'companion_name': request.form.get('companion_name'),
                'companion_role': request.form.get('companion_role'),
                'meal_type': request.form.get('meal_type'),
                'dining_time': dining_time.strftime('%Y-%m-%d %H:%M:%S'),
                'taste_rating': request.form.get('taste_rating', type=int),
                'hygiene_rating': request.form.get('hygiene_rating', type=int),
                'service_rating': request.form.get('service_rating', type=int),
                'comments': request.form.get('comments') or None,
                'suggestions': request.form.get('suggestions') or None
            }

            # 执行 SQL
            result = db.session.execute(sql, params)
            companion_id = result.fetchone()[0]

            # 处理照片上传
            if 'photos' in request.files:
                photo_paths = []
                for photo in request.files.getlist('photos'):
                    if photo.filename:
                        photo_path = handle_photo_upload(photo, 'companion')
                        if photo_path:
                            photo_paths.append(photo_path)

                if photo_paths:
                    # 更新照片路径
                    sql = text("""
                    UPDATE dining_companions
                    SET photo_paths = :photo_paths
                    WHERE id = :id
                    """)

                    db.session.execute(sql, {
                        'photo_paths': ';'.join(photo_paths),
                        'id': companion_id
                    })

            db.session.commit()
            flash('陪餐记录添加成功', 'success')
            return redirect(url_for('daily_management.companions', log_id=log_id))
        except Exception as e:
            db.session.rollback()
            flash(f'保存失败: {str(e)}', 'danger')

    # 创建空表单对象，用于生成 CSRF 令牌
    form = FlaskForm()

    if is_iframe:
        # 如果是在iframe中加载，使用简化模板
        return render_template('daily_management/add_companion_iframe.html',
                              title='添加陪餐记录',
                              log=log,
                              form=form)
    else:
        # 正常加载完整页面
        return render_template('daily_management/add_companion.html',
                              title='添加陪餐记录',
                              log=log,
                              form=form)

# 查看陪餐记录详情
@daily_management_bp.route('/companions/view/<int:companion_id>')
@login_required
def view_companion(companion_id):
    # 使用原始 SQL 查询获取陪餐记录
    sql = text("""
    SELECT * FROM dining_companions WHERE id = :id
    """)
    result = db.session.execute(sql, {'id': companion_id})
    companion = result.fetchone()

    if not companion:
        abort(404)

    # 获取照片
    photos = []
    if companion.photo_paths:
        photo_paths = companion.photo_paths.split(';')
        photos = [{'file_path': path} for path in photo_paths]

    # 创建空表单对象，用于生成 CSRF 令牌
    form = FlaskForm()

    return render_template('daily_management/view_companion.html',
                          title='陪餐记录详情',
                          companion=companion,
                          photos=photos,
                          form=form)

# 打印单个陪餐记录详情
@daily_management_bp.route('/companions/print_detail/<int:companion_id>')
@login_required
def print_companion_detail(companion_id):
    # 使用原始 SQL 查询获取陪餐记录
    sql = text("""
    SELECT dc.*, dl.area_id
    FROM dining_companions dc
    LEFT JOIN daily_logs dl ON dc.daily_log_id = dl.id
    WHERE dc.id = :id
    """)
    result = db.session.execute(sql, {'id': companion_id})
    companion = result.fetchone()

    if not companion:
        abort(404)

    # 获取学校信息
    sql = text("""
    SELECT * FROM administrative_areas WHERE id = :id
    """)
    result = db.session.execute(sql, {'id': companion.area_id})
    area = result.fetchone()

    # 创建一个包含所有必要信息的字典
    companion_data = {
        'id': companion.id,
        'companion_name': companion.companion_name,
        'companion_role': companion.companion_role,
        'meal_type': companion.meal_type,
        'dining_time': companion.dining_time,
        'taste_rating': companion.taste_rating,
        'hygiene_rating': companion.hygiene_rating,
        'service_rating': companion.service_rating,
        'comments': companion.comments,
        'suggestions': companion.suggestions,
        'photo_paths': companion.photo_paths,
        'daily_log': {
            'area': {
                'id': area.id,
                'name': area.name
            }
        }
    }

    # 获取照片
    photos = []
    if companion.photo_paths:
        photo_paths = companion.photo_paths.split(';')
        photos = [{'file_path': path} for path in photo_paths]

    return render_template('daily_management/print_companion.html',
                          title='打印陪餐记录',
                          companion=companion_data,
                          photos=photos,
                          now=datetime.now())

# 编辑陪餐记录
@daily_management_bp.route('/companions/edit/<int:companion_id>', methods=['GET', 'POST'])
@login_required
def edit_companion(companion_id):
    # 使用原始 SQL 查询获取陪餐记录
    sql = text("""
    SELECT * FROM dining_companions WHERE id = :id
    """)
    result = db.session.execute(sql, {'id': companion_id})
    companion = result.fetchone()

    if not companion:
        abort(404)

    if request.method == 'POST':
        try:
            # 使用原始 SQL 语句更新陪餐记录
            sql = text("""
            UPDATE dining_companions
            SET companion_name = :companion_name,
                companion_role = :companion_role,
                meal_type = :meal_type,
                dining_time = :dining_time,
                taste_rating = :taste_rating,
                hygiene_rating = :hygiene_rating,
                service_rating = :service_rating,
                comments = :comments,
                suggestions = :suggestions
            WHERE id = :id
            """)

            # 准备参数
            dining_time = datetime.strptime(f"{request.form.get('dining_date')} {request.form.get('dining_time')}", '%Y-%m-%d %H:%M')
            params = {
                'id': companion_id,
                'companion_name': request.form.get('companion_name'),
                'companion_role': request.form.get('companion_role'),
                'meal_type': request.form.get('meal_type'),
                'dining_time': dining_time.strftime('%Y-%m-%d %H:%M:%S'),
                'taste_rating': request.form.get('taste_rating', type=int),
                'hygiene_rating': request.form.get('hygiene_rating', type=int),
                'service_rating': request.form.get('service_rating', type=int),
                'comments': request.form.get('comments') or None,
                'suggestions': request.form.get('suggestions') or None
            }

            # 执行 SQL
            db.session.execute(sql, params)

            # 处理照片上传
            if 'photos' in request.files:
                new_photo_paths = []

                # 保留现有照片路径
                if companion.photo_paths:
                    new_photo_paths = companion.photo_paths.split(';')

                for photo in request.files.getlist('photos'):
                    if photo.filename:
                        photo_path = handle_photo_upload(photo, 'companion')
                        if photo_path:
                            new_photo_paths.append(photo_path)

                if new_photo_paths:
                    # 更新照片路径
                    sql = text("""
                    UPDATE dining_companions
                    SET photo_paths = :photo_paths
                    WHERE id = :id
                    """)

                    db.session.execute(sql, {
                        'photo_paths': ';'.join(new_photo_paths),
                        'id': companion_id
                    })

            db.session.commit()
            flash('陪餐记录更新成功', 'success')
            return redirect(url_for('daily_management.view_companion', companion_id=companion_id))
        except Exception as e:
            db.session.rollback()
            flash(f'保存失败: {str(e)}', 'danger')

    # 获取照片
    photos = []
    if companion.photo_paths:
        photo_paths = companion.photo_paths.split(';')
        photos = [{'file_path': path} for path in photo_paths]

    # 创建空表单对象，用于生成 CSRF 令牌
    form = FlaskForm()

    return render_template('daily_management/edit_companion.html',
                          title='编辑陪餐记录',
                          companion=companion,
                          photos=photos,
                          form=form)

# 删除陪餐记录
@daily_management_bp.route('/companions/delete/<int:companion_id>', methods=['POST'])
@login_required
def delete_companion(companion_id):
    # 使用原始 SQL 查询获取陪餐记录
    sql = text("""
    SELECT * FROM dining_companions WHERE id = :id
    """)
    result = db.session.execute(sql, {'id': companion_id})
    companion = result.fetchone()

    if not companion:
        abort(404)

    log_id = companion.daily_log_id

    try:
        # 删除关联的照片文件
        if companion.photo_paths:
            photo_paths = companion.photo_paths.split(';')
            for path in photo_paths:
                file_path = os.path.join(current_app.static_folder, path.lstrip('/static/'))
                if os.path.exists(file_path):
                    os.remove(file_path)

        # 使用原始 SQL 语句删除陪餐记录
        sql = text("""
        DELETE FROM dining_companions
        WHERE id = :id
        """)

        db.session.execute(sql, {'id': companion_id})
        db.session.commit()

        flash('陪餐记录已删除', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'删除失败: {str(e)}', 'danger')

    return redirect(url_for('daily_management.companions', log_id=log_id))

# 培训记录
@daily_management_bp.route('/trainings/<int:log_id>')
@login_required
def trainings(log_id):
    """培训记录列表"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法查看培训记录', 'danger')
        return redirect(url_for('main.index'))

    # 获取日志并确保属于当前用户的学校
    log = DailyLog.query.filter_by(id=log_id, area_id=user_area.id).first_or_404()

    # 使用原始 SQL 查询获取培训记录
    sql = text("""
    SELECT * FROM canteen_training_records
    WHERE daily_log_id = :log_id
    ORDER BY training_time DESC
    """)

    result = db.session.execute(sql, {'log_id': log_id})
    trainings = result.fetchall()

    return render_template('daily_management/trainings.html',
                          title=f'{user_area.name} - 培训记录',
                          log=log,
                          trainings=trainings,
                          school=user_area)

@daily_management_bp.route('/trainings/add/<int:log_id>', methods=['GET', 'POST'])
@login_required
def add_training(log_id):
    """添加培训记录"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法添加培训记录', 'danger')
        return redirect(url_for('main.index'))

    # 获取日志并确保属于当前用户的学校
    log = DailyLog.query.filter_by(id=log_id, area_id=user_area.id).first_or_404()

    # 获取当前时间，用于表单默认值
    now = datetime.now()

    if request.method == 'POST':
        try:
            # 使用原始 SQL 语句创建培训记录，避免 datetime 精度问题
            training_date_time = datetime.strptime(f"{request.form.get('training_date')} {request.form.get('training_time')}", '%Y-%m-%d %H:%M')

            sql = text("""
            INSERT INTO canteen_training_records
            (daily_log_id, training_topic, trainer, training_time, location,
            duration, attendees_count, content_summary, effectiveness_evaluation,
            created_by, area_id)
            OUTPUT inserted.id
            VALUES
            (:daily_log_id, :training_topic, :trainer, :training_time, :location,
            :duration, :attendees_count, :content_summary, :effectiveness_evaluation,
            :created_by, :area_id)
            """)

            # 准备参数
            params = {
                'daily_log_id': log_id,
                'training_topic': request.form.get('training_topic'),
                'trainer': request.form.get('trainer'),
                'training_time': training_date_time.strftime('%Y-%m-%d %H:%M:%S'),
                'location': request.form.get('location') or None,
                'duration': request.form.get('duration', type=int) or None,
                'attendees_count': request.form.get('attendees_count', type=int) or None,
                'content_summary': request.form.get('content_summary') or None,
                'effectiveness_evaluation': request.form.get('effectiveness_evaluation') or None,
                'created_by': current_user.id,
                'area_id': current_user.area_id
            }

            # 执行 SQL
            result = db.session.execute(sql, params)
            training_id = result.fetchone()[0]

            # 处理照片上传
            if 'photos' in request.files:
                photo_paths = []
                for photo in request.files.getlist('photos'):
                    if photo.filename:
                        photo_path = handle_photo_upload(photo, 'training')
                        if photo_path:
                            photo_paths.append(photo_path)

                if photo_paths:
                    # 更新照片路径
                    sql = text("""
                    UPDATE canteen_training_records
                    SET photo_paths = :photo_paths
                    WHERE id = :id
                    """)

                    db.session.execute(sql, {
                        'photo_paths': ';'.join(photo_paths),
                        'id': training_id
                    })

            db.session.commit()
            flash('培训记录添加成功', 'success')
            return redirect(url_for('daily_management.trainings', log_id=log_id))
        except Exception as e:
            db.session.rollback()
            flash(f'保存失败: {str(e)}', 'danger')

    return render_template('daily_management/add_training.html',
                          title=f'{user_area.name} - 添加培训记录',
                          log=log,
                          now=now,
                          school=user_area)

@daily_management_bp.route('/trainings/view/<int:training_id>')
@login_required
def view_training(training_id):
    """查看培训记录详情"""
    # 使用原始 SQL 查询获取培训记录
    sql = text("""
    SELECT * FROM canteen_training_records WHERE id = :id
    """)
    result = db.session.execute(sql, {'id': training_id})
    training = result.fetchone()

    if not training:
        abort(404)

    # 获取关联的日志
    log = DailyLog.query.get(training.daily_log_id) if training.daily_log_id else None

    # 获取照片
    photos = []
    if training.photo_paths:
        photo_paths = training.photo_paths.split(';')
        photos = [{'file_path': path} for path in photo_paths]

    return render_template('daily_management/view_training.html',
                          title='培训记录详情',
                          training=training,
                          log=log,
                          photos=photos)

# 打印单个培训记录详情
@daily_management_bp.route('/trainings/print_detail/<int:training_id>')
@login_required
def print_training_detail(training_id):
    """打印单个培训记录详情"""
    # 使用原始 SQL 查询获取培训记录
    sql = text("""
    SELECT ctr.*, dl.area_id
    FROM canteen_training_records ctr
    LEFT JOIN daily_logs dl ON ctr.daily_log_id = dl.id
    WHERE ctr.id = :id
    """)
    result = db.session.execute(sql, {'id': training_id})
    training = result.fetchone()

    if not training:
        abort(404)

    # 获取学校信息
    sql = text("""
    SELECT * FROM administrative_areas WHERE id = :id
    """)
    result = db.session.execute(sql, {'id': training.area_id})
    area = result.fetchone()

    # 创建一个包含所有必要信息的字典
    training_data = {
        'id': training.id,
        'training_topic': training.training_topic,
        'trainer': training.trainer,
        'training_time': training.training_time,
        'location': training.location,
        'duration': training.duration,
        'attendees_count': training.attendees_count,
        'content_summary': training.content_summary,
        'effectiveness_evaluation': training.effectiveness_evaluation,
        'photo_paths': training.photo_paths,
        'created_at': training.created_at,
        'daily_log': {
            'area': {
                'id': area.id,
                'name': area.name
            }
        }
    }

    # 获取照片
    photos = []
    if training.photo_paths:
        photo_paths = training.photo_paths.split(';')
        photos = [{'file_path': path} for path in photo_paths]

    return render_template('daily_management/print_training.html',
                          title='打印培训记录',
                          training=training_data,
                          photos=photos,
                          now=datetime.now())

@daily_management_bp.route('/trainings/edit/<int:training_id>', methods=['GET', 'POST'])
@login_required
def edit_training(training_id):
    """编辑培训记录"""
    training = CanteenTrainingRecord.query.get_or_404(training_id)

    if request.method == 'POST':
        try:
            # 使用原始 SQL 语句更新培训记录，避免 datetime 精度问题
            training_date_time = datetime.strptime(f"{request.form.get('training_date')} {request.form.get('training_time')}", '%Y-%m-%d %H:%M')

            sql = text("""
            UPDATE canteen_training_records
            SET training_topic = :training_topic,
                trainer = :trainer,
                training_time = :training_time,
                location = :location,
                duration = :duration,
                attendees_count = :attendees_count,
                content_summary = :content_summary,
                effectiveness_evaluation = :effectiveness_evaluation
            WHERE id = :id
            """)

            # 准备参数
            params = {
                'id': training_id,
                'training_topic': request.form.get('training_topic'),
                'trainer': request.form.get('trainer'),
                'training_time': training_date_time.strftime('%Y-%m-%d %H:%M:%S'),
                'location': request.form.get('location') or None,
                'duration': request.form.get('duration', type=int) or None,
                'attendees_count': request.form.get('attendees_count', type=int) or None,
                'content_summary': request.form.get('content_summary') or None,
                'effectiveness_evaluation': request.form.get('effectiveness_evaluation') or None
            }

            # 执行 SQL
            db.session.execute(sql, params)

            # 处理照片上传
            if 'photos' in request.files:
                photo_paths = []
                # 保留现有照片路径
                if training.photo_paths:
                    photo_paths = training.photo_paths.split(';')

                for photo in request.files.getlist('photos'):
                    if photo.filename:
                        photo_path = handle_photo_upload(photo, 'training')
                        if photo_path:
                            photo_paths.append(photo_path)

                if photo_paths:
                    # 更新照片路径
                    sql = text("""
                    UPDATE canteen_training_records
                    SET photo_paths = :photo_paths
                    WHERE id = :id
                    """)

                    db.session.execute(sql, {
                        'photo_paths': ';'.join(photo_paths),
                        'id': training_id
                    })

            db.session.commit()
            flash('培训记录更新成功', 'success')
            return redirect(url_for('daily_management.view_training', training_id=training_id))
        except Exception as e:
            db.session.rollback()
            flash(f'保存失败: {str(e)}', 'danger')

    return render_template('daily_management/edit_training.html',
                          title='编辑培训记录',
                          training=training)

@daily_management_bp.route('/trainings/delete/<int:training_id>', methods=['POST'])
@login_required
def delete_training(training_id):
    """删除培训记录"""
    # 使用原始 SQL 查询获取培训记录
    sql = text("""
    SELECT * FROM canteen_training_records WHERE id = :id
    """)
    result = db.session.execute(sql, {'id': training_id})
    training = result.fetchone()

    if not training:
        abort(404)

    log_id = training.daily_log_id

    try:
        # 删除关联的照片文件
        if training.photo_paths:
            photo_paths = training.photo_paths.split(';')
            for path in photo_paths:
                file_path = os.path.join(current_app.static_folder, path.lstrip('/static/'))
                if os.path.exists(file_path):
                    os.remove(file_path)

        # 使用原始 SQL 语句删除培训记录
        sql = text("""
        DELETE FROM canteen_training_records
        WHERE id = :id
        """)

        db.session.execute(sql, {'id': training_id})
        db.session.commit()

        flash('培训记录已删除', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'删除失败: {str(e)}', 'danger')

    return redirect(url_for('daily_management.trainings', log_id=log_id))

# 特殊事件
@daily_management_bp.route('/events/<int:log_id>')
@login_required
def events(log_id):
    """特殊事件列表"""
    # 使用原始 SQL 查询获取日志
    log_sql = text("""
        SELECT id, FORMAT(log_date, 'yyyy-MM-dd') as log_date
        FROM daily_logs
        WHERE id = :log_id
    """)
    log_result = db.session.execute(log_sql, {'log_id': log_id}).fetchone()
    if not log_result:
        abort(404)

    log = {
        'id': log_result[0],
        'log_date': log_result[1]
    }

    # 使用原始 SQL 查询获取特殊事件列表，在SQL中格式化时间
    events_sql = text("""
        SELECT
            id,
            event_type,
            FORMAT(event_time, 'yyyy-MM-dd HH:mm') as event_time,
            description,
            participants
        FROM special_events
        WHERE daily_log_id = :log_id
        ORDER BY event_time DESC
    """)

    events_result = db.session.execute(events_sql, {'log_id': log_id}).fetchall()
    events = []
    for row in events_result:
        events.append({
            'id': row[0],
            'event_type': row[1],
            'event_time': row[2],
            'description': row[3],
            'participants': row[4]
        })

    form = FlaskForm()  # 创建表单对象以处理 CSRF

    # 获取用户所属学校
    user_area = current_user.get_current_area()

    return render_template('daily_management/events.html',
                          title=f'{user_area.name} - 特殊事件',
                          log=log,
                          events=events,
                          form=form,
                          school=user_area)

@daily_management_bp.route('/events/add/<int:log_id>', methods=['GET', 'POST'])
@login_required
def add_event(log_id):
    """添加特殊事件"""
    # 使用原始 SQL 获取日志
    log_sql = text("""
        SELECT id, FORMAT(log_date, 'yyyy-MM-dd') as log_date
        FROM daily_logs
        WHERE id = :log_id
    """)
    log_result = db.session.execute(log_sql, {'log_id': log_id}).fetchone()
    if not log_result:
        abort(404)

    log = {
        'id': log_result[0],
        'log_date': log_result[1]
    }

    if request.method == 'POST':
        try:
            # 使用原始 SQL 创建记录，使用CAST确保时间格式正确
            sql = text("""
                INSERT INTO special_events
                (daily_log_id, event_type, event_time, description, participants,
                handling_measures, event_summary, photo_paths)
                OUTPUT inserted.id
                VALUES
                (:daily_log_id, :event_type, CAST(:event_time AS DATETIME2(1)), :description, :participants,
                :handling_measures, :event_summary, :photo_paths)
            """)

            # 处理事件时间
            event_time = f"{request.form.get('event_date')} {request.form.get('event_time')}"

            params = {
                'daily_log_id': log_id,
                'event_type': request.form.get('event_type'),
                'event_time': event_time,
                'description': request.form.get('description'),
                'participants': request.form.get('participants'),
                'handling_measures': request.form.get('handling_measures'),
                'event_summary': request.form.get('event_summary'),
                'photo_paths': None
            }

            result = db.session.execute(sql, params)
            event_id = result.fetchone()[0]

            # 处理照片上传
            if 'photos' in request.files:
                photo_paths = []
                for photo in request.files.getlist('photos'):
                    if photo.filename:
                        photo_path = handle_photo_upload(photo, 'event')
                        if photo_path:
                            photo_paths.append(photo_path)

                if photo_paths:
                    # 更新照片路径
                    update_sql = text("""
                        UPDATE special_events
                        SET photo_paths = :photo_paths
                        WHERE id = :id
                    """)
                    db.session.execute(update_sql, {
                        'photo_paths': ';'.join(photo_paths),
                        'id': event_id
                    })

            db.session.commit()
            flash('特殊事件添加成功', 'success')
            return redirect(url_for('daily_management.events', log_id=log_id))

        except Exception as e:
            db.session.rollback()
            flash(f'保存失败: {str(e)}', 'danger')

    return render_template('daily_management/add_event.html',
                          title='添加特殊事件',
                          log=log)

@daily_management_bp.route('/events/view/<int:event_id>')
@login_required
def view_event(event_id):
    """查看特殊事件详情"""
    # 使用原始 SQL 查询获取事件详情
    event_sql = text("""
        SELECT
            e.id,
            e.event_type,
            FORMAT(e.event_time, 'yyyy-MM-dd HH:mm') as event_time,
            e.description,
            e.participants,
            e.handling_measures,
            e.event_summary,
            e.photo_paths,
            FORMAT(e.created_at, 'yyyy-MM-dd HH:mm') as created_at,
            e.daily_log_id,
            FORMAT(d.log_date, 'yyyy-MM-dd') as log_date
        FROM special_events e
        JOIN daily_logs d ON e.daily_log_id = d.id
        WHERE e.id = :event_id
    """)

    event_result = db.session.execute(event_sql, {'event_id': event_id}).fetchone()
    if not event_result:
        abort(404)

    event = {
        'id': event_result[0],
        'event_type': event_result[1],
        'event_time': event_result[2],
        'description': event_result[3],
        'participants': event_result[4],
        'handling_measures': event_result[5],
        'event_summary': event_result[6],
        'photo_paths': event_result[7],
        'created_at': event_result[8],
        'daily_log_id': event_result[9],
        'daily_log': {
            'log_date': event_result[10]
        }
    }

    # 从 event.photo_paths 获取照片路径
    photos = []
    if event['photo_paths']:
        photo_paths = event['photo_paths'].split(';')
        photos = [{'file_path': path} for path in photo_paths]

    return render_template('daily_management/view_event.html',
                          title='特殊事件详情',
                          event=event,
                          photos=photos)

@daily_management_bp.route('/events/edit/<int:event_id>', methods=['GET', 'POST'])
@login_required
def edit_event(event_id):
    """编辑特殊事件"""
    event = SpecialEvent.query.get_or_404(event_id)
    form = FlaskForm()  # 创建表单对象以处理 CSRF

    if form.validate_on_submit():  # 替换 request.method == 'POST'
        event.event_type = request.form.get('event_type')
        event.event_time = datetime.strptime(f"{request.form.get('event_date')} {request.form.get('event_time')}", '%Y-%m-%d %H:%M')
        event.description = request.form.get('description')
        event.participants = request.form.get('participants')
        event.handling_measures = request.form.get('handling_measures')
        event.event_summary = request.form.get('event_summary')

        try:
            db.session.commit()

            # 处理照片上传
            if 'photos' in request.files:
                photo_paths = []
                # 保留现有照片路径
                if event.photo_paths:
                    photo_paths = event.photo_paths.split(';')

                for photo in request.files.getlist('photos'):
                    if photo.filename:
                        photo_path = handle_photo_upload(photo, 'event')
                        if photo_path:
                            photo_paths.append(photo_path)

                if photo_paths:
                    event.photo_paths = ';'.join(photo_paths)
                    db.session.commit()

            flash('特殊事件更新成功', 'success')
            return redirect(url_for('daily_management.view_event', event_id=event_id))
        except Exception as e:
            db.session.rollback()
            flash(f'保存失败: {str(e)}', 'danger')

    return render_template('daily_management/edit_event.html',
                          title='编辑特殊事件',
                          event=event,
                          form=form)

@daily_management_bp.route('/events/delete/<int:event_id>', methods=['POST'])
@login_required
def delete_event(event_id):
    """删除特殊事件"""
    form = FlaskForm()  # 创建表单对象以处理 CSRF

    if form.validate_on_submit():  # 验证 CSRF 令牌
        event = SpecialEvent.query.get_or_404(event_id)
        log_id = event.daily_log_id

        try:
            # 删除关联的照片文件
            if event.photo_paths:
                photo_paths = event.photo_paths.split(';')
                for path in photo_paths:
                    file_path = os.path.join(current_app.static_folder, path.lstrip('/static/'))
                    if os.path.exists(file_path):
                        os.remove(file_path)

            # 删除特殊事件
            db.session.delete(event)
            db.session.commit()

            flash('特殊事件已删除', 'success')
        except Exception as e:
            db.session.rollback()
            flash(f'删除失败: {str(e)}', 'danger')

        return redirect(url_for('daily_management.events', log_id=log_id))

    flash('无效的请求', 'danger')
    return redirect(url_for('daily_management.events', log_id=event.daily_log_id))

# 问题记录
@daily_management_bp.route('/issues/<int:log_id>')
@login_required
def issues(log_id):
    """问题记录列表"""
    log = DailyLog.query.get_or_404(log_id)

    # 使用原生 SQL 查询
    sql = text("""
        SELECT
            id,
            issue_type,
            description,
            status,
            found_time,
            responsible_person
        FROM issues
        WHERE daily_log_id = :log_id
        ORDER BY found_time DESC
    """)

    result = db.session.execute(sql, {'log_id': log_id})
    issues = []
    for row in result:
        issue = {
            'id': row.id,
            'issue_type': row.issue_type,
            'description': row.description,
            'status': row.status,
            'found_time': row.found_time,
            'responsible_person': row.responsible_person
        }
        issues.append(issue)

    # 获取用户所属学校
    user_area = current_user.get_current_area()

    return render_template('daily_management/issues.html',
                          title=f'{user_area.name} - 问题记录',
                          log=log,
                          issues=issues,
                          school=user_area)

@daily_management_bp.route('/issues/add/<int:log_id>', methods=['GET', 'POST'])
@login_required
def add_issue(log_id):
    """添加问题记录"""
    log = DailyLog.query.get_or_404(log_id)

    if request.method == 'POST':
        try:
            # 使用原生 SQL 插入记录
            sql = text("""
                INSERT INTO issues
                (daily_log_id, issue_type, description, status, found_time, responsible_person)
                OUTPUT inserted.id
                VALUES
                (:daily_log_id, :issue_type, :description, :status, :found_time, :responsible_person)
            """)

            # 解析时间
            found_time = datetime.strptime(
                f"{request.form.get('found_date')} {request.form.get('found_time')}",
                '%Y-%m-%d %H:%M'
            ).replace(microsecond=0)

            params = {
                'daily_log_id': log_id,
                'issue_type': request.form.get('issue_type'),
                'description': request.form.get('description'),
                'status': 'pending',
                'found_time': found_time,
                'responsible_person': request.form.get('responsible_person')
            }

            result = db.session.execute(sql, params)
            issue_id = result.fetchone()[0]

            # 处理照片上传
            if 'photos' in request.files:
                photo_paths = []
                for photo in request.files.getlist('photos'):
                    if photo.filename:
                        photo_path = handle_photo_upload(photo, 'issue')
                        if photo_path:
                            photo_paths.append(photo_path)

                if photo_paths:
                    # 更新照片路径
                    update_sql = text("""
                        UPDATE issues
                        SET photo_paths = :photo_paths
                        WHERE id = :id
                    """)
                    db.session.execute(update_sql, {
                        'photo_paths': ';'.join(photo_paths),
                        'id': issue_id
                    })

            db.session.commit()
            flash('问题记录添加成功', 'success')
            return redirect(url_for('daily_management.issues', log_id=log_id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(str(e))
            flash(f'保存失败: {str(e)}', 'danger')

    return render_template('daily_management/add_issue.html',
                          title='添加问题记录',
                          log=log,
                          now=datetime.now())

@daily_management_bp.route('/issues/view/<int:issue_id>')
@login_required
def view_issue(issue_id):
    """查看问题记录详情"""
    # 使用原生 SQL 查询问题记录
    sql = text("""
        SELECT i.*, d.log_date
        FROM issues i
        JOIN daily_logs d ON i.daily_log_id = d.id
        WHERE i.id = :issue_id
    """)

    result = db.session.execute(sql, {'issue_id': issue_id})
    issue = result.fetchone()
    if not issue:
        abort(404)

    # 构建问题记录字典
    issue_dict = {
        'id': issue.id,
        'daily_log_id': issue.daily_log_id,
        'issue_type': issue.issue_type,
        'description': issue.description,
        'status': issue.status,
        'found_time': issue.found_time,
        'fixed_time': issue.fixed_time,
        'responsible_person': issue.responsible_person,
        'verification_result': issue.verification_result,
        'photo_paths': issue.photo_paths,
        'daily_log': {
            'log_date': issue.log_date
        }
    }

    # 处理照片
    photos = []
    if issue_dict['photo_paths']:
        photo_paths = issue_dict['photo_paths'].split(';')
        photos = [{'file_path': path} for path in photo_paths]

    return render_template('daily_management/view_issue.html',
                          title='问题记录详情',
                          issue=issue_dict,
                          photos=photos)

@daily_management_bp.route('/issues/edit/<int:issue_id>', methods=['GET', 'POST'])
@login_required
def edit_issue(issue_id):
    """编辑问题记录"""
    # 使用原生 SQL 查询问题记录
    sql = text("""
        SELECT i.*, d.log_date
        FROM issues i
        JOIN daily_logs d ON i.daily_log_id = d.id
        WHERE i.id = :issue_id
    """)

    result = db.session.execute(sql, {'issue_id': issue_id})
    issue = result.fetchone()
    if not issue:
        abort(404)

    # 构建问题记录字典
    issue_dict = {
        'id': issue.id,
        'daily_log_id': issue.daily_log_id,
        'issue_type': issue.issue_type,
        'description': issue.description,
        'status': issue.status,
        'found_time': issue.found_time,
        'fixed_time': issue.fixed_time,
        'responsible_person': issue.responsible_person,
        'verification_result': issue.verification_result,
        'photo_paths': issue.photo_paths,
        'daily_log': {
            'log_date': issue.log_date
        }
    }

    if request.method == 'POST':
        try:
            # 解析时间
            found_time = datetime.strptime(
                f"{request.form.get('found_date')} {request.form.get('found_time')}",
                '%Y-%m-%d %H:%M'
            ).replace(microsecond=0)

            # 更新问题记录
            update_sql = text("""
                UPDATE issues
                SET issue_type = :issue_type,
                    description = :description,
                    status = :status,
                    found_time = :found_time,
                    responsible_person = :responsible_person,
                    verification_result = :verification_result,
                    fixed_time = CASE
                        WHEN :status = 'fixed' AND fixed_time IS NULL
                        THEN :current_time
                        ELSE fixed_time
                    END
                WHERE id = :id
            """)

            params = {
                'id': issue_id,
                'issue_type': request.form.get('issue_type'),
                'description': request.form.get('description'),
                'status': request.form.get('status'),
                'found_time': found_time,
                'responsible_person': request.form.get('responsible_person'),
                'verification_result': request.form.get('verification_result'),
                'current_time': datetime.now().replace(microsecond=0)
            }

            db.session.execute(update_sql, params)

            # 处理照片上传
            if 'photos' in request.files:
                photo_paths = []
                # 保留现有照片路径
                if issue_dict['photo_paths']:
                    photo_paths = issue_dict['photo_paths'].split(';')

                for photo in request.files.getlist('photos'):
                    if photo.filename:
                        photo_path = handle_photo_upload(photo, 'issue')
                        if photo_path:
                            photo_paths.append(photo_path)

                if photo_paths:
                    # 更新照片路径
                    update_photo_sql = text("""
                        UPDATE issues
                        SET photo_paths = :photo_paths
                        WHERE id = :id
                    """)
                    db.session.execute(update_photo_sql, {
                        'photo_paths': ';'.join(photo_paths),
                        'id': issue_id
                    })

            db.session.commit()
            flash('问题记录更新成功', 'success')
            return redirect(url_for('daily_management.view_issue', issue_id=issue_id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(str(e))
            flash(f'保存失败: {str(e)}', 'danger')

    # 处理照片
    photos = []
    if issue_dict['photo_paths']:
        photo_paths = issue_dict['photo_paths'].split(';')
        photos = [{'file_path': path} for path in photo_paths]

    return render_template('daily_management/edit_issue.html',
                          title='编辑问题记录',
                          issue=issue_dict,
                          photos=photos)

@daily_management_bp.route('/issues/delete/<int:issue_id>', methods=['POST'])
@login_required
def delete_issue(issue_id):
    """删除问题记录"""
    issue = Issue.query.get_or_404(issue_id)
    log_id = issue.daily_log_id

    try:
        # 删除关联的照片文件
        issue = Issue.query.get_or_404(issue_id)
        if issue.photo_paths:
            photo_paths = issue.photo_paths.split(';')
            for path in photo_paths:
                file_path = os.path.join(current_app.static_folder, path.lstrip('/static/'))
                if os.path.exists(file_path):
                    os.remove(file_path)

        # 删除问题记录
        db.session.delete(issue)
        db.session.commit()

        flash('问题记录已删除', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'删除失败: {str(e)}', 'danger')

    return redirect(url_for('daily_management.issues', log_id=log_id))

# 辅助函数
def get_inspection_type_name(inspection_type):
    names = {
        'morning': '晨检',
        'noon': '午检',
        'evening': '晚检'
    }
    return names.get(inspection_type, '检查')

def handle_photo_upload(photo_file, reference_type):
    """处理照片上传，返回照片路径"""
    if photo_file and photo_file.filename:
        filename = secure_filename(photo_file.filename)
        # 生成唯一文件名
        unique_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{filename}"

        # 确保目录存在
        upload_folder = os.path.join(
            current_app.static_folder,
            'uploads',
            'daily_management',
            reference_type
        )
        os.makedirs(upload_folder, exist_ok=True)

        # 处理图片
        img = Image.open(photo_file)

        # 调整大小为800x600，保持宽高比
        img.thumbnail((800, 600))

        # 保存处理后的图片
        file_path = os.path.join(upload_folder, unique_filename)
        img.save(file_path)

        # 返回照片路径
        return f"/static/uploads/daily_management/{reference_type}/{unique_filename}"

    return None

# 这些路由已移至 photo_routes.py 文件中
# 上传检查记录照片、更新照片评分和删除照片的路由

# 单独保存检查项
@daily_management_bp.route('/inspections/save-item', methods=['POST'])
@login_required
def save_inspection_item():
    """单独保存检查项

    参数:
        item: 检查项目名称
        description: 检查备注
        inspection_id: 检查记录ID (如果是新记录，则为 'new')
        period: 检查类型 (morning, noon, evening)

    返回:
        JSON格式的保存结果
    """
    if request.headers.get('X-Requested-With') != 'XMLHttpRequest':
        return jsonify({'success': False, 'message': '不支持的请求类型'}), 400

    # 记录请求数据，便于调试
    current_app.logger.debug(f"接收到的表单数据: {request.form}")
    current_app.logger.debug(f"请求头: {request.headers}")

    # 获取并验证表单数据
    item = request.form.get('item')
    description = request.form.get('description')
    inspection_id = request.form.get('inspection_id')
    period = request.form.get('period')

    # 详细的参数验证
    if not item:
        current_app.logger.warning("缺少检查项名称")
        return jsonify({'success': False, 'message': '检查项名称不能为空'}), 400

    if not period:
        current_app.logger.warning("缺少检查时段")
        return jsonify({'success': False, 'message': '检查时段不能为空'}), 400

    if period not in ['morning', 'noon', 'evening']:
        current_app.logger.warning(f"检查时段无效: {period}")
        return jsonify({'success': False, 'message': '检查时段无效，必须是 morning/noon/evening'}), 400

    # description 可以为空，但需要初始化为空字符串
    description = description or ''

    try:
        # 获取当前日期的日志
        today = date.today()
        log = DailyLog.query.filter_by(log_date=today).first()

        # 如果日志不存在，创建一个新的日志
        if not log:
            # 创建新日志 - 注意不包含 created_at 和 updated_at 字段，让数据库使用默认值
            sql = text("""
            INSERT INTO daily_logs
            (log_date, area_id, created_by)
            OUTPUT inserted.id
            VALUES
            (CONVERT(DATE, :log_date, 23), :area_id, :created_by)
            """)

            # 准备参数
            params = {
                'log_date': today.strftime('%Y-%m-%d'),
                'area_id': current_user.area_id,
                'created_by': current_user.id
            }

            # 执行 SQL
            result = db.session.execute(sql, params)
            log_id = result.fetchone()[0]

            # 获取新创建的日志
            log = DailyLog.query.get(log_id)

        # 检查是否已存在该检查项
        if inspection_id == 'new':
            # 创建新记录 - 使用原始 SQL 语句，不包含时间字段，让数据库使用默认值
            sql = text("""
            INSERT INTO inspection_records
            (daily_log_id, inspection_type, inspection_item, status, description, inspector_id)
            OUTPUT inserted.id
            VALUES
            (:daily_log_id, :inspection_type, :inspection_item, :status, :description, :inspector_id)
            """)

            # 准备参数
            params = {
                'daily_log_id': log.id,
                'inspection_type': period,
                'inspection_item': item,
                'status': 'normal',  # 默认状态
                'description': description,
                'inspector_id': current_user.id
            }

            # 执行 SQL
            try:
                result = db.session.execute(sql, params)
                record_id = result.fetchone()[0]
                db.session.commit()

                current_app.logger.info(f"成功创建检查记录: ID={record_id}, 项目={item}")

                return jsonify({
                    'success': True,
                    'message': f'{item}检查记录已保存',
                    'inspection_id': record_id
                })
            except Exception as e:
                db.session.rollback()
                error_msg = f"SQL执行失败: {str(e)}"
                current_app.logger.error(error_msg)
                import traceback
                current_app.logger.error(traceback.format_exc())
                return jsonify({'success': False, 'message': error_msg}), 500
        else:
            # 更新现有记录 - 使用原始 SQL 语句，不包含时间字段，让数据库使用默认值
            sql = text("""
            UPDATE inspection_records
            SET description = :description, inspector_id = :inspector_id
            WHERE id = :id
            """)

            # 准备参数
            params = {
                'description': description,
                'inspector_id': current_user.id,
                'id': inspection_id
            }

            # 执行 SQL
            try:
                db.session.execute(sql, params)
                db.session.commit()

                current_app.logger.info(f"成功更新检查记录: ID={inspection_id}, 项目={item}")

                return jsonify({
                    'success': True,
                    'message': f'{item}检查记录已更新',
                    'inspection_id': inspection_id
                })
            except Exception as e:
                db.session.rollback()
                error_msg = f"SQL执行失败: {str(e)}"
                current_app.logger.error(error_msg)
                import traceback
                current_app.logger.error(traceback.format_exc())
                return jsonify({'success': False, 'message': error_msg}), 500
    except Exception as e:
        db.session.rollback()
        import traceback
        error_msg = f'保存检查项失败: {str(e)}'
        current_app.logger.error(error_msg)
        current_app.logger.error(traceback.format_exc())
        return jsonify({'success': False, 'message': error_msg}), 500

# 获取检查项详情展示接口
@daily_management_bp.route('/inspections/display/<int:inspection_id>', methods=['GET'])
def display_inspection(inspection_id):
    """展示检查项详情，包括照片、评分和情况说明

    参数:
        inspection_id: 检查记录ID

    返回:
        JSON格式的检查项详情，包括照片、评分和情况说明
    """
    try:
        # 获取检查记录
        inspection = InspectionRecord.query.get_or_404(inspection_id)

        # 获取关联的照片
        photos = Photo.query.filter_by(
            reference_type='inspection',
            reference_id=inspection_id
        ).order_by(Photo.created_at.desc()).all()

        # 构建照片数据
        photo_data = []
        for photo in photos:
            photo_data.append({
                'id': photo.id,
                'url': photo.file_path,
                'rating': photo.rating or 0,
                'created_at': photo.upload_time.strftime('%Y-%m-%d %H:%M') if photo.upload_time else ''
            })

        # 构建检查项数据
        inspection_data = {
            'id': inspection.id,
            'item': inspection.inspection_item,
            'type': inspection.inspection_type,
            'type_name': get_inspection_type_name(inspection.inspection_type),
            'status': inspection.status,
            'description': inspection.description,
            'inspector_id': inspection.inspector_id,
            'inspector_name': inspection.inspector.name if inspection.inspector else '',
            'inspection_time': inspection.inspection_time.strftime('%Y-%m-%d %H:%M') if inspection.inspection_time else '',
            'photos': photo_data,
            'avg_rating': sum([p.rating or 0 for p in photos]) / len(photos) if photos else 0
        }

        # 返回JSON格式的检查项详情
        return jsonify({
            'success': True,
            'data': inspection_data
        })
    except Exception as e:
        current_app.logger.error(f"获取检查项详情失败: {str(e)}")
        import traceback
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f"获取检查项详情失败: {str(e)}"
        }), 500

# 获取检查项详情HTML展示接口
@daily_management_bp.route('/inspections/display-html/<int:inspection_id>', methods=['GET'])
def display_inspection_html(inspection_id):
    """展示检查项详情的HTML片段，包括照片、评分和情况说明

    参数:
        inspection_id: 检查记录ID

    返回:
        HTML片段，用于嵌入到其他页面
    """
    try:
        # 获取检查记录
        inspection = InspectionRecord.query.get_or_404(inspection_id)

        # 获取关联的照片
        photos = Photo.query.filter_by(
            reference_type='inspection',
            reference_id=inspection_id
        ).order_by(Photo.created_at.desc()).all()

        # 渲染HTML片段
        return render_template(
            'daily_management/inspection_display.html',
            inspection=inspection,
            photos=photos,
            type_name=get_inspection_type_name(inspection.inspection_type)
        )
    except Exception as e:
        current_app.logger.error(f"获取检查项HTML详情失败: {str(e)}")
        import traceback
        current_app.logger.error(traceback.format_exc())
        return f"<div class='alert alert-danger'>获取检查项详情失败: {str(e)}</div>"

# 获取最新检查项HTML展示接口
@daily_management_bp.route('/inspections/latest-html', methods=['GET'])
def latest_inspection_html():
    """获取最新的检查项HTML展示

    查询参数:
        type: 检查类型 (morning, noon, evening)
        item: 检查项目名称
        days: 查询最近几天的记录 (默认1天)

    返回:
        HTML片段，用于嵌入到其他页面
    """
    try:
        # 获取查询参数
        inspection_type = request.args.get('type')
        inspection_item = request.args.get('item')
        days = int(request.args.get('days', 1))

        # 限制查询天数，防止查询过多数据
        if days > 30:
            days = 30

        # 计算日期范围
        from datetime import datetime, timedelta
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # 构建查询
        query = InspectionRecord.query

        # 添加日期范围条件
        query = query.filter(InspectionRecord.inspection_time >= start_date)

        # 添加检查类型条件
        if inspection_type:
            query = query.filter(InspectionRecord.inspection_type == inspection_type)

        # 添加检查项目名称条件
        if inspection_item:
            query = query.filter(InspectionRecord.inspection_item == inspection_item)

        # 按时间倒序排序并获取第一条记录
        inspection = query.order_by(InspectionRecord.inspection_time.desc()).first()

        # 如果没有找到记录，返回提示信息
        if not inspection:
            return "<div class='alert alert-info'>暂无检查记录</div>"

        # 获取关联的照片
        photos = Photo.query.filter_by(
            reference_type='inspection',
            reference_id=inspection.id
        ).order_by(Photo.created_at.desc()).all()

        # 渲染HTML片段
        return render_template(
            'daily_management/inspection_display.html',
            inspection=inspection,
            photos=photos,
            type_name=get_inspection_type_name(inspection.inspection_type)
        )
    except Exception as e:
        current_app.logger.error(f"获取最新检查项HTML详情失败: {str(e)}")
        import traceback
        current_app.logger.error(traceback.format_exc())
        return f"<div class='alert alert-danger'>获取检查项详情失败: {str(e)}</div>"

# 检查记录表格视图
@daily_management_bp.route('/inspections/table/<int:log_id>')
@login_required
def inspections_table(log_id):
    """检查记录表格视图

    参数:
        log_id: 日志ID

    返回:
        渲染后的表格视图页面
    """
    log = DailyLog.query.get_or_404(log_id)

    # 获取前一天和后一天的日志
    prev_log = DailyLog.query.filter(DailyLog.log_date < log.log_date).order_by(DailyLog.log_date.desc()).first()
    next_log = DailyLog.query.filter(DailyLog.log_date > log.log_date).order_by(DailyLog.log_date.asc()).first()

    # 获取检查记录
    morning_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='morning').all()
    noon_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='noon').all()
    evening_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='evening').all()

    # 获取每个检查项的照片
    try:
        for inspection_list in [morning_inspections, noon_inspections, evening_inspections]:
            for inspection in inspection_list:
                # 使用原始SQL查询获取照片
                sql = text("""
                SELECT id, file_path, rating, upload_time
                FROM photos
                WHERE reference_type = 'inspection' AND reference_id = :reference_id
                ORDER BY upload_time DESC
                """)

                result = db.session.execute(sql, {'reference_id': inspection.id})

                photos = []
                for row in result:
                    photos.append({
                        'id': row[0],
                        'file_path': row[1],
                        'rating': row[2],
                        'upload_time': row[3]
                    })

                inspection.photos = photos
    except Exception as e:
        # 如果photos表不存在，忽略错误
        current_app.logger.error(f"获取照片失败: {str(e)}")
        pass

    # 获取今天的日期，用于日期导航
    today = date.today()

    # 导入 timedelta，用于日期计算
    from datetime import timedelta

    return render_template('daily_management/inspections_table.html',
                          title='检查记录表格视图',
                          log=log,
                          prev_log=prev_log,
                          next_log=next_log,
                          morning_inspections=morning_inspections,
                          noon_inspections=noon_inspections,
                          evening_inspections=evening_inspections,
                          today=today,
                          timedelta=timedelta)

# 通过日期访问检查记录
@daily_management_bp.route('/inspections/date/<date_str>', methods=['GET'])
@login_required
def inspections_by_date(date_str):
    """通过日期访问检查记录

    参数:
        date_str: 日期字符串，格式为 YYYY-MM-DD

    返回:
        重定向到对应日期的检查记录页面
    """
    try:
        # 解析日期
        log_date = datetime.strptime(date_str, '%Y-%m-%d').date()

        # 查找对应日期的日志
        log = DailyLog.query.filter_by(log_date=log_date).first()

        # 获取视图类型参数
        view_type = request.args.get('view', 'card')

        # 如果找到日志，重定向到检查记录页面
        if log:
            if view_type == 'table':
                return redirect(url_for('daily_management.inspections_table', log_id=log.id))
            elif view_type == 'card_layout':
                return redirect(url_for('daily_management.inspections_card_layout', log_id=log.id))
            elif view_type == 'simple_table':
                return redirect(url_for('daily_management.inspections_simple_table', log_id=log.id))
            elif view_type == 'category_cards':
                return redirect(url_for('daily_management.inspections_category_cards', log_id=log.id))
            elif view_type == 'simplified':
                return redirect(url_for('daily_management.simplified_inspection', log_id=log.id))
            else:
                # 默认使用简化检查记录页面
                return redirect(url_for('daily_management.simplified_inspection', log_id=log.id))

        # 如果没有找到日志，创建一个新的日志
        sql = text("""
        INSERT INTO daily_logs
        (log_date, area_id, created_by)
        OUTPUT inserted.id
        VALUES
        (CONVERT(DATE, :log_date, 23), :area_id, :created_by)
        """)

        # 准备参数
        params = {
            'log_date': log_date.strftime('%Y-%m-%d'),
            'area_id': current_user.area_id,
            'created_by': current_user.id
        }

        # 执行 SQL
        result = db.session.execute(sql, params)
        log_id = result.fetchone()[0]
        db.session.commit()

        # 重定向到新创建的日志的检查记录页面
        if view_type == 'table':
            return redirect(url_for('daily_management.inspections_table', log_id=log_id))
        elif view_type == 'card_layout':
            return redirect(url_for('daily_management.inspections_card_layout', log_id=log_id))
        elif view_type == 'simple_table':
            return redirect(url_for('daily_management.inspections_simple_table', log_id=log_id))
        elif view_type == 'category_cards':
            return redirect(url_for('daily_management.inspections_category_cards', log_id=log_id))
        elif view_type == 'simplified':
            return redirect(url_for('daily_management.simplified_inspection', log_id=log_id))
        else:
            # 默认使用简化检查记录页面
            return redirect(url_for('daily_management.simplified_inspection', log_id=log_id))
    except ValueError:
        flash('日期格式无效，请使用 YYYY-MM-DD 格式', 'danger')
        return redirect(url_for('daily_management.logs'))
    except Exception as e:
        db.session.rollback()
        flash(f'访问检查记录失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.logs'))

# 检查记录表格 HTML 片段
@daily_management_bp.route('/inspections/table-html/<int:log_id>', methods=['GET'])
def inspection_table_html(log_id):
    """返回检查记录表格的 HTML 片段

    参数:
        log_id: 日志ID

    返回:
        HTML 片段
    """
    try:
        log = DailyLog.query.get_or_404(log_id)

        # 获取检查记录
        morning_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='morning').all()
        noon_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='noon').all()
        evening_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='evening').all()

        # 获取每个检查项的照片
        try:
            for inspection_list in [morning_inspections, noon_inspections, evening_inspections]:
                for inspection in inspection_list:
                    # 使用原始SQL查询获取照片
                    sql = text("""
                    SELECT id, file_path, rating, upload_time
                    FROM photos
                    WHERE reference_type = 'inspection' AND reference_id = :reference_id
                    ORDER BY upload_time DESC
                    """)

                    result = db.session.execute(sql, {'reference_id': inspection.id})

                    photos = []
                    for row in result:
                        photos.append({
                            'id': row[0],
                            'file_path': row[1],
                            'rating': row[2],
                            'upload_time': row[3]
                        })

                    inspection.photos = photos
        except Exception as e:
            # 如果photos表不存在，忽略错误
            current_app.logger.error(f"获取照片失败: {str(e)}")
            pass

        # 渲染 HTML 片段
        return render_template('daily_management/inspection_table_html.html',
                              log=log,
                              morning_inspections=morning_inspections,
                              noon_inspections=noon_inspections,
                              evening_inspections=evening_inspections)
    except Exception as e:
        current_app.logger.error(f"获取检查记录表格 HTML 片段失败: {str(e)}")
        import traceback
        current_app.logger.error(traceback.format_exc())
        return "<div class='alert alert-danger'>获取检查记录失败</div>"

# 检查记录卡片布局视图
@daily_management_bp.route('/inspections/card-layout/<int:log_id>')
@login_required
def inspections_card_layout(log_id):
    """检查记录卡片布局视图

    参数:
        log_id: 日志ID

    返回:
        渲染后的卡片布局视图页面
    """
    log = DailyLog.query.get_or_404(log_id)

    # 获取前一天和后一天的日志
    prev_log = DailyLog.query.filter(DailyLog.log_date < log.log_date).order_by(DailyLog.log_date.desc()).first()
    next_log = DailyLog.query.filter(DailyLog.log_date > log.log_date).order_by(DailyLog.log_date.asc()).first()

    # 获取检查记录
    morning_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='morning').all()
    noon_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='noon').all()
    evening_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='evening').all()

    # 获取每个检查项的照片
    try:
        for inspection_list in [morning_inspections, noon_inspections, evening_inspections]:
            for inspection in inspection_list:
                # 使用原始SQL查询获取照片
                sql = text("""
                SELECT id, file_path, rating, upload_time
                FROM photos
                WHERE reference_type = 'inspection' AND reference_id = :reference_id
                ORDER BY upload_time DESC
                """)

                result = db.session.execute(sql, {'reference_id': inspection.id})

                photos = []
                for row in result:
                    photos.append({
                        'id': row[0],
                        'file_path': row[1],
                        'rating': row[2],
                        'upload_time': row[3]
                    })

                inspection.photos = photos
    except Exception as e:
        # 如果photos表不存在，忽略错误
        current_app.logger.error(f"获取照片失败: {str(e)}")
        pass

    # 获取今天的日期，用于日期导航
    today = date.today()

    # 导入 timedelta，用于日期计算
    from datetime import timedelta

    return render_template('daily_management/inspections_card_layout.html',
                          title='检查记录卡片布局视图',
                          log=log,
                          prev_log=prev_log,
                          next_log=next_log,
                          morning_inspections=morning_inspections,
                          noon_inspections=noon_inspections,
                          evening_inspections=evening_inspections,
                          today=today,
                          timedelta=timedelta)

# 检查类别卡片视图
@daily_management_bp.route('/inspections/category-cards/<int:log_id>')
@login_required
def inspections_category_cards(log_id):
    """检查类别卡片视图 - 按照6个检查类别组织的卡片式布局

    参数:
        log_id: 日志ID

    返回:
        渲染后的类别卡片视图页面
    """
    log = DailyLog.query.get_or_404(log_id)

    # 获取前一天和后一天的日志
    prev_log = DailyLog.query.filter(DailyLog.log_date < log.log_date).order_by(DailyLog.log_date.desc()).first()
    next_log = DailyLog.query.filter(DailyLog.log_date > log.log_date).order_by(DailyLog.log_date.asc()).first()

    # 定义检查类别
    categories = ['地面卫生', '操作台卫生', '设备卫生', '食材存储', '人员卫生', '餐具消毒']

    # 获取所有检查记录
    all_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id).all()

    # 按类别和时段组织检查记录
    category_inspections = {}
    for category in categories:
        category_inspections[category] = {
            'morning': None,
            'noon': None,
            'evening': None
        }

    # 填充检查记录
    for inspection in all_inspections:
        if inspection.inspection_item in categories:
            category_inspections[inspection.inspection_item][inspection.inspection_type] = inspection

    # 获取每个检查项的照片
    category_photos = {}
    try:
        for inspection in all_inspections:
            if inspection.inspection_item in categories:
                # 使用原始SQL查询获取照片
                sql = text("""
                SELECT id, file_path, rating, upload_time
                FROM photos
                WHERE reference_type = 'inspection' AND reference_id = :reference_id
                ORDER BY upload_time DESC
                """)

                result = db.session.execute(sql, {'reference_id': inspection.id})

                photos = []
                for row in result:
                    photos.append({
                        'id': row[0],
                        'file_path': row[1],
                        'rating': row[2],
                        'upload_time': row[3]
                    })

                # 为检查记录添加照片
                inspection.photos = photos

                # 为类别添加主照片
                if photos and inspection.inspection_item not in category_photos:
                    category_photos[inspection.inspection_item] = {
                        'main': photos[0]
                    }
                elif photos and 'main' not in category_photos[inspection.inspection_item]:
                    category_photos[inspection.inspection_item]['main'] = photos[0]

                # 调试日志
                current_app.logger.info(f"检查项目 {inspection.inspection_item} 的照片数量: {len(photos)}")
                if photos:
                    current_app.logger.info(f"第一张照片路径: {photos[0]['file_path']}")
    except Exception as e:
        # 如果photos表不存在，忽略错误
        current_app.logger.error(f"获取照片失败: {str(e)}")
        import traceback
        current_app.logger.error(traceback.format_exc())

    # 获取今天的日期，用于日期导航
    today = date.today()

    # 导入 timedelta，用于日期计算
    from datetime import timedelta

    return render_template('daily_management/inspections_category_cards.html',
                          title='检查类别卡片视图',
                          log=log,
                          prev_log=prev_log,
                          next_log=next_log,
                          categories=categories,
                          category_inspections=category_inspections,
                          category_photos=category_photos,
                          today=today,
                          timedelta=timedelta)

# 检查记录简单表格视图
@daily_management_bp.route('/inspections/simple-table/<int:log_id>')
@login_required
def inspections_simple_table(log_id):
    """检查记录简单表格视图

    参数:
        log_id: 日志ID

    返回:
        渲染后的简单表格视图页面
    """
    log = DailyLog.query.get_or_404(log_id)

    # 获取前一天和后一天的日志
    prev_log = DailyLog.query.filter(DailyLog.log_date < log.log_date).order_by(DailyLog.log_date.desc()).first()
    next_log = DailyLog.query.filter(DailyLog.log_date > log.log_date).order_by(DailyLog.log_date.asc()).first()

    # 获取检查记录
    morning_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='morning').all()
    noon_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='noon').all()
    evening_inspections = InspectionRecord.query.filter_by(daily_log_id=log_id, inspection_type='evening').all()

    # 获取每个检查项的照片
    try:
        for inspection_list in [morning_inspections, noon_inspections, evening_inspections]:
            for inspection in inspection_list:
                # 使用原始SQL查询获取照片
                sql = text("""
                SELECT id, file_path, rating, upload_time
                FROM photos
                WHERE reference_type = 'inspection' AND reference_id = :reference_id
                ORDER BY upload_time DESC
                """)

                result = db.session.execute(sql, {'reference_id': inspection.id})

                photos = []
                for row in result:
                    photos.append({
                        'id': row[0],
                        'file_path': row[1],
                        'rating': row[2],
                        'upload_time': row[3]
                    })

                inspection.photos = photos
    except Exception as e:
        # 如果photos表不存在，忽略错误
        current_app.logger.error(f"获取照片失败: {str(e)}")
        pass

    # 获取今天的日期，用于日期导航
    today = date.today()

    # 导入 timedelta，用于日期计算
    from datetime import timedelta

    return render_template('daily_management/inspections_simple_table.html',
                          title='检查记录简单表格视图',
                          log=log,
                          prev_log=prev_log,
                          next_log=next_log,
                          morning_inspections=morning_inspections,
                          noon_inspections=noon_inspections,
                          evening_inspections=evening_inspections,
                          today=today,
                          timedelta=timedelta)

# 通过日期获取检查记录表格 HTML 片段
@daily_management_bp.route('/inspections/table-html-by-date/<date_str>', methods=['GET'])
def inspection_table_html_by_date(date_str):
    """通过日期返回检查记录表格的 HTML 片段

    参数:
        date_str: 日期字符串，格式为 YYYY-MM-DD

    返回:
        HTML 片段
    """
    try:
        # 解析日期
        log_date = datetime.strptime(date_str, '%Y-%m-%d').date()

        # 查找对应日期的日志
        log = DailyLog.query.filter_by(log_date=log_date).first()

        # 如果没有找到日志，返回提示信息
        if not log:
            return "<div class='alert alert-info'>该日期暂无检查记录</div>"

        # 重定向到检查记录表格 HTML 片段接口
        return inspection_table_html(log.id)
    except ValueError:
        return "<div class='alert alert-danger'>日期格式无效，请使用 YYYY-MM-DD 格式</div>"
    except Exception as e:
        current_app.logger.error(f"通过日期获取检查记录表格 HTML 片段失败: {str(e)}")
        import traceback
        current_app.logger.error(traceback.format_exc())
        return "<div class='alert alert-danger'>获取检查记录失败</div>"

# 检查项展示演示页面
@daily_management_bp.route('/inspections/demo', methods=['GET'])
@login_required
def inspection_demo():
    """检查项展示演示页面

    返回:
        渲染后的演示页面
    """
    return render_template('daily_management/inspection_demo.html')

# 检查记录小组件演示页面
@daily_management_bp.route('/inspections/widget-demo', methods=['GET'])
@login_required
def inspection_widget_demo():
    """检查记录小组件演示页面

    返回:
        渲染后的演示页面
    """
    return render_template('daily_management/inspection_widget_demo.html')

# 获取检查类型名称的辅助函数
def get_inspection_type_name(inspection_type):
    type_names = {
        'morning': '晨检',
        'noon': '午检',
        'evening': '晚检'
    }
    return type_names.get(inspection_type, '检查')

# 打印检查记录
@daily_management_bp.route('/print/inspection/<date>')
@login_required
def print_inspection(date):
    """打印检查记录"""
    try:
        target_date = datetime.strptime(date, '%Y-%m-%d').date()
    except ValueError:
        flash('无效的日期格式', 'danger')
        return redirect(url_for('daily_management.index'))

    # 查找对应日期的日志
    log = DailyLog.query.filter_by(log_date=target_date).first()
    if not log:
        flash('该日期没有工作日志', 'warning')
        return redirect(url_for('daily_management.index'))

    # 获取检查记录
    morning_inspections = InspectionRecord.query.filter_by(daily_log_id=log.id, inspection_type='morning').all()
    noon_inspections = InspectionRecord.query.filter_by(daily_log_id=log.id, inspection_type='noon').all()
    evening_inspections = InspectionRecord.query.filter_by(daily_log_id=log.id, inspection_type='evening').all()

    # 渲染打印模板
    return render_template('daily_management/print/inspection_report.html',
                          log=log,
                          morning_inspections=morning_inspections,
                          noon_inspections=noon_inspections,
                          evening_inspections=evening_inspections,
                          print_date=datetime.now())

# 生成陪餐记录二维码
@daily_management_bp.route('/companions/qrcode/<int:log_id>')
@login_required
def generate_companion_qrcode_view(log_id):
    """生成陪餐记录二维码"""
    log = DailyLog.query.get_or_404(log_id)

    # 获取学校信息
    school = None
    if log.area_id:
        school = AdministrativeArea.query.get(log.area_id)

    if not school:
        flash('无法获取学校信息', 'danger')
        return redirect(url_for('daily_management.companions', log_id=log_id))

    try:
        # 生成二维码 - 使用入口页面
        qrcode_path = generate_companion_qrcode(school.id, log_id=None, use_entry=True)
        qrcode_base64 = generate_qrcode_base64(url_for('daily_management.companion_entry',
                                                      school_id=school.id,
                                                      _external=True))

        # 同时生成直接添加页面的二维码
        direct_qrcode_path = generate_companion_qrcode(school.id, log_id=log_id)
        direct_qrcode_base64 = generate_qrcode_base64(url_for('daily_management.public_add_companion',
                                                             school_id=school.id,
                                                             log_id=log_id,
                                                             _external=True))

        return render_template('daily_management/companion_qrcode.html',
                              title='陪餐记录二维码',
                              log=log,
                              school=school,
                              qrcode_path=qrcode_path,
                              qrcode_base64=qrcode_base64,
                              direct_qrcode_path=direct_qrcode_path,
                              direct_qrcode_base64=direct_qrcode_base64)
    except Exception as e:
        flash(f'生成二维码失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.companions', log_id=log_id))

# 学校陪餐二维码生成器
@daily_management_bp.route('/companions/school-qrcode')
@login_required
def school_qrcode():
    """学校陪餐二维码生成器 - 为每个学校生成固定的二维码"""
    # 获取用户当前区域
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法使用此功能', 'danger')
        return redirect(url_for('daily_management.index'))

    # 直接使用用户的区域ID作为学校ID
    school_id = user_area.id
    current_app.logger.info(f"使用用户当前区域: {user_area.name} (ID: {user_area.id})")

    # 获取用户可访问的学校列表 (只包括用户所属区域及其下级区域)
    accessible_areas = current_user.get_accessible_areas()
    schools = [area for area in accessible_areas if area.level == 3]  # level=3 表示学校

    # 按名称排序
    schools.sort(key=lambda x: x.name)

    selected_school = None
    qrcode_path = None
    qrcode_base64 = None
    direct_qrcode_path = None
    direct_qrcode_base64 = None

    # 生成二维码
    try:
        # 获取学校信息
        selected_school = user_area

        # 生成二维码 - 使用入口页面
        qrcode_path = generate_companion_qrcode(selected_school.id, log_id=None, use_entry=True)
        entry_url = url_for('daily_management.companion_entry', school_id=selected_school.id, _external=True)
        current_app.logger.info(f"生成入口二维码，URL: {entry_url}")
        qrcode_base64 = generate_qrcode_base64(entry_url)

        # 同时生成直接添加页面的二维码
        direct_qrcode_path = generate_companion_qrcode(selected_school.id, log_id=None)
        direct_url = url_for('daily_management.public_add_companion', school_id=selected_school.id, _external=True)
        current_app.logger.info(f"生成直接添加二维码，URL: {direct_url}")
        direct_qrcode_base64 = generate_qrcode_base64(direct_url)

        # 检查二维码数据是否生成成功
        if not qrcode_base64 or not direct_qrcode_base64:
            flash('二维码生成失败，请重试', 'warning')
            current_app.logger.error(f"二维码数据为空: entry={bool(qrcode_base64)}, direct={bool(direct_qrcode_base64)}")
    except Exception as e:
        flash(f'生成二维码失败: {str(e)}', 'danger')

    return render_template('daily_management/school_qrcode.html',
                          title='学校陪餐二维码生成器',
                          schools=schools,
                          selected_school=selected_school,
                          qrcode_path=qrcode_path,
                          qrcode_base64=qrcode_base64,
                          direct_qrcode_path=direct_qrcode_path,
                          direct_qrcode_base64=direct_qrcode_base64)

# 陪餐记录主入口
@daily_management_bp.route('/public/companions/entry/<int:school_id>')
def companion_entry(school_id):
    """陪餐记录主入口"""
    # 获取学校信息
    school = AdministrativeArea.query.get_or_404(school_id)

    # 获取今天的日期
    today = date.today()

    # 创建空表单对象，用于生成 CSRF 令牌
    form = FlaskForm()

    return render_template('daily_management/companion_entry.html',
                          title=f'{school.name} - 陪餐记录',
                          school=school,
                          today=today,
                          form=form)

# 公开访问的陪餐记录添加页面
@daily_management_bp.route('/public/companions/add/<int:school_id>')
def public_add_companion(school_id):
    """公开访问的陪餐记录添加页面"""
    log_id = request.args.get('log_id', type=int)

    # 获取学校信息
    school = AdministrativeArea.query.get_or_404(school_id)

    # 如果提供了日志ID，则获取该日志
    log = None
    if log_id:
        log = DailyLog.query.get_or_404(log_id)
    else:
        # 否则获取今天的日志
        today = date.today()
        log = DailyLog.query.filter_by(log_date=today, area_id=school_id).first()

        # 如果今天没有日志，则创建一个
        if not log:
            try:
                # 使用原始SQL创建日志
                sql = text("""
                INSERT INTO daily_logs
                (log_date, area_id, manager, student_count, teacher_count, other_count)
                OUTPUT inserted.id
                VALUES
                (CONVERT(DATE, :log_date, 23), :area_id, :manager, :student_count, :teacher_count, :other_count)
                """)

                params = {
                    'log_date': today.strftime('%Y-%m-%d'),
                    'area_id': school_id,
                    'manager': '系统自动创建',
                    'student_count': 0,
                    'teacher_count': 0,
                    'other_count': 0
                }

                result = db.session.execute(sql, params)
                log_id = result.fetchone()[0]
                db.session.commit()

                # 获取新创建的日志
                log = DailyLog.query.get(log_id)
            except Exception as e:
                current_app.logger.error(f"创建日志失败: {str(e)}")
                abort(500)

    # 创建空表单对象，用于生成 CSRF 令牌
    form = FlaskForm()

    return render_template('daily_management/public_add_companion.html',
                          title=f'{school.name} - 添加陪餐记录',
                          school=school,
                          log=log,
                          form=form,
                          today=date.today())

# 处理公开提交的陪餐记录
@daily_management_bp.route('/public/companions/submit/<int:log_id>', methods=['POST'])
def public_submit_companion(log_id):
    """处理公开提交的陪餐记录"""
    log = DailyLog.query.get_or_404(log_id)
    form = FlaskForm()

    if form.validate_on_submit():
        try:
            # 使用原始 SQL 语句创建陪餐记录
            sql = text("""
            INSERT INTO dining_companions
            (daily_log_id, companion_name, companion_role, meal_type, dining_time,
            taste_rating, hygiene_rating, service_rating, comments, suggestions)
            OUTPUT inserted.id
            VALUES
            (:daily_log_id, :companion_name, :companion_role, :meal_type, :dining_time,
            :taste_rating, :hygiene_rating, :service_rating, :comments, :suggestions)
            """)

            # 准备参数
            dining_time = datetime.strptime(f"{request.form.get('dining_date')} {request.form.get('dining_time')}", '%Y-%m-%d %H:%M')
            params = {
                'daily_log_id': log_id,
                'companion_name': request.form.get('companion_name'),
                'companion_role': request.form.get('companion_role'),
                'meal_type': request.form.get('meal_type'),
                'dining_time': dining_time.strftime('%Y-%m-%d %H:%M:%S'),
                'taste_rating': request.form.get('taste_rating', type=int),
                'hygiene_rating': request.form.get('hygiene_rating', type=int),
                'service_rating': request.form.get('service_rating', type=int),
                'comments': request.form.get('comments') or None,
                'suggestions': request.form.get('suggestions') or None
            }

            # 执行 SQL
            result = db.session.execute(sql, params)
            companion_id = result.fetchone()[0]

            # 处理照片上传
            if 'photos' in request.files:
                photo_paths = []
                for photo in request.files.getlist('photos'):
                    if photo.filename:
                        photo_path = handle_photo_upload(photo, 'companion')
                        if photo_path:
                            photo_paths.append(photo_path)

                if photo_paths:
                    # 更新照片路径
                    sql = text("""
                    UPDATE dining_companions
                    SET photo_paths = :photo_paths
                    WHERE id = :id
                    """)

                    db.session.execute(sql, {
                        'photo_paths': ';'.join(photo_paths),
                        'id': companion_id
                    })

            db.session.commit()

            # 获取学校信息
            school = AdministrativeArea.query.get(log.area_id) if log.area_id else None
            school_name = school.name if school else "学校"

            return render_template('daily_management/public_success.html',
                                  title='提交成功',
                                  school_name=school_name)
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"保存陪餐记录失败: {str(e)}")
            return render_template('daily_management/public_error.html',
                                  title='提交失败',
                                  error=str(e))

    # 表单验证失败
    return redirect(url_for('daily_management.public_add_companion',
                           school_id=log.area_id,
                           log_id=log_id))

# 打印陪餐记录（通过日期）
@daily_management_bp.route('/print/companion/<date>')
@login_required
def print_companion(date):
    """打印陪餐记录（通过日期）"""
    try:
        target_date = datetime.strptime(date, '%Y-%m-%d').date()
    except ValueError:
        flash('无效的日期格式', 'danger')
        return redirect(url_for('daily_management.index'))

    # 查找对应日期的日志
    log = DailyLog.query.filter_by(log_date=target_date).first()
    if not log:
        flash('该日期没有工作日志', 'warning')
        return redirect(url_for('daily_management.index'))

    # 获取陪餐记录
    companions = DiningCompanion.query.filter_by(daily_log_id=log.id).all()

    # 渲染打印模板
    return render_template('daily_management/print/companion_report.html',
                          log=log,
                          companions=companions,
                          print_date=datetime.now())

# 打印陪餐记录（通过日志ID）
@daily_management_bp.route('/print/companion-by-id/<int:log_id>')
@login_required
def print_companion_by_id(log_id):
    """打印陪餐记录（通过日志ID）"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限查看该日志的陪餐记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取陪餐记录
    companions = DiningCompanion.query.filter_by(daily_log_id=log.id).all()

    # 渲染打印模板
    return render_template('daily_management/print/companion_report.html',
                          log=log,
                          companions=companions,
                          print_date=datetime.now())

# 打印所有陪餐记录（通过日志ID）
@daily_management_bp.route('/print/companions/<int:log_id>')
@login_required
def print_companions(log_id):
    """打印所有陪餐记录（通过日志ID）"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限查看该日志的陪餐记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取陪餐记录
    try:
        # 使用原始 SQL 查询获取陪餐记录
        companions_sql = text("""
        SELECT * FROM dining_companions
        WHERE daily_log_id = :log_id
        ORDER BY dining_time
        """)

        companions_result = db.session.execute(companions_sql, {'log_id': log.id})
        companions = []

        for row in companions_result:
            companion = DiningCompanion()
            for key in row.keys():
                if hasattr(companion, key):
                    setattr(companion, key, row[key])
            companions.append(companion)
    except Exception as e:
        current_app.logger.error(f"获取陪餐记录失败: {str(e)}")
        companions = []

    # 渲染打印模板
    return render_template('daily_management/print/companions_report.html',
                          log=log,
                          companions=companions,
                          print_date=datetime.now())

# 打印培训记录（通过日期）
@daily_management_bp.route('/print/training/<date>')
@login_required
def print_training(date):
    """打印培训记录（通过日期）"""
    try:
        target_date = datetime.strptime(date, '%Y-%m-%d').date()
    except ValueError:
        flash('无效的日期格式', 'danger')
        return redirect(url_for('daily_management.index'))

    # 查找对应日期的日志
    log = DailyLog.query.filter_by(log_date=target_date).first()
    if not log:
        flash('该日期没有工作日志', 'warning')
        return redirect(url_for('daily_management.index'))

    # 获取培训记录
    trainings = CanteenTrainingRecord.query.filter_by(daily_log_id=log.id).all()

    # 渲染打印模板
    return render_template('daily_management/print/training_report.html',
                          log=log,
                          trainings=trainings,
                          print_date=datetime.now())

# 打印培训记录（通过日志ID）
@daily_management_bp.route('/print/training-by-id/<int:log_id>')
@login_required
def print_training_by_id(log_id):
    """打印培训记录（通过日志ID）"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限查看该日志的培训记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取培训记录
    trainings = CanteenTrainingRecord.query.filter_by(daily_log_id=log.id).all()

    # 渲染打印模板
    return render_template('daily_management/print/training_report.html',
                          log=log,
                          trainings=trainings,
                          print_date=datetime.now())

# 打印所有培训记录（通过日志ID）
@daily_management_bp.route('/print/trainings/<int:log_id>')
@login_required
def print_trainings(log_id):
    """打印所有培训记录（通过日志ID）"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限查看该日志的培训记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 获取培训记录
    try:
        # 使用原始 SQL 查询获取培训记录
        trainings_sql = text("""
        SELECT * FROM canteen_training_records
        WHERE daily_log_id = :log_id
        ORDER BY training_time
        """)

        trainings_result = db.session.execute(trainings_sql, {'log_id': log.id})
        trainings = []

        for row in trainings_result:
            training = CanteenTrainingRecord()
            for key in row.keys():
                if hasattr(training, key):
                    setattr(training, key, row[key])
            trainings.append(training)
    except Exception as e:
        current_app.logger.error(f"获取培训记录失败: {str(e)}")
        trainings = []

    # 渲染打印模板
    return render_template('daily_management/print/trainings_report.html',
                          log=log,
                          trainings=trainings,
                          print_date=datetime.now())

# 打印特殊事件
@daily_management_bp.route('/print/event/<date>')
@login_required
def print_event(date):
    """打印特殊事件"""
    try:
        target_date = datetime.strptime(date, '%Y-%m-%d').date()
    except ValueError:
        flash('无效的日期格式', 'danger')
        return redirect(url_for('daily_management.index'))

    # 查找对应日期的日志
    log = DailyLog.query.filter_by(log_date=target_date).first()
    if not log:
        flash('该日期没有工作日志', 'warning')
        return redirect(url_for('daily_management.index'))

    # 获取特殊事件
    events = SpecialEvent.query.filter_by(daily_log_id=log.id).all()

    # 渲染打印模板
    return render_template('daily_management/print/event_report.html',
                          log=log,
                          events=events,
                          print_date=datetime.now())

# 打印问题记录
@daily_management_bp.route('/print/issue/<date>')
@login_required
def print_issue(date):
    """打印问题记录"""
    try:
        target_date = datetime.strptime(date, '%Y-%m-%d').date()
    except ValueError:
        flash('无效的日期格式', 'danger')
        return redirect(url_for('daily_management.index'))

    # 查找对应日期的问题记录
    issues = Issue.query.filter(
        Issue.found_time >= target_date,
        Issue.found_time < target_date + timedelta(days=1)
    ).all()

    # 渲染打印模板
    return render_template('daily_management/print/issue_report.html',
                          date=target_date,
                          issues=issues,
                          print_date=datetime.now())

# 打印日志汇总
@daily_management_bp.route('/print/daily-summary/<date>')
@login_required
def print_daily_summary(date):
    """打印日志汇总"""
    try:
        target_date = datetime.strptime(date, '%Y-%m-%d').date()
    except ValueError:
        flash('无效的日期格式', 'danger')
        return redirect(url_for('daily_management.index'))

    # 查找对应日期的日志
    log = DailyLog.query.filter_by(log_date=target_date).first()
    if not log:
        flash('该日期没有工作日志', 'warning')
        return redirect(url_for('daily_management.index'))

    # 获取各类记录
    morning_inspections = InspectionRecord.query.filter_by(daily_log_id=log.id, inspection_type='morning').all()
    noon_inspections = InspectionRecord.query.filter_by(daily_log_id=log.id, inspection_type='noon').all()
    evening_inspections = InspectionRecord.query.filter_by(daily_log_id=log.id, inspection_type='evening').all()
    companions = DiningCompanion.query.filter_by(daily_log_id=log.id).all()
    trainings = CanteenTrainingRecord.query.filter_by(daily_log_id=log.id).all()
    events = SpecialEvent.query.filter_by(daily_log_id=log.id).all()
    issues = Issue.query.filter(
        Issue.found_time >= target_date,
        Issue.found_time < target_date + timedelta(days=1)
    ).all()

    # 渲染打印模板
    return render_template('daily_management/print/daily_summary.html',
                          log=log,
                          morning_inspections=morning_inspections,
                          noon_inspections=noon_inspections,
                          evening_inspections=evening_inspections,
                          companions=companions,
                          trainings=trainings,
                          events=events,
                          issues=issues,
                          print_date=datetime.now())

# 公开访问的照片上传页面
@daily_management_bp.route('/public/photo-upload/<int:school_id>/<int:log_id>')
def public_photo_upload(school_id, log_id):
    """公开访问的照片上传页面，用于员工扫码上传照片

    参数:
        school_id: 学校ID
        log_id: 日志ID

    返回:
        渲染后的照片上传页面
    """
    # 获取学校信息
    school = AdministrativeArea.query.get_or_404(school_id)

    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 确保日志属于该学校
    if log.area_id != school_id:
        abort(404)

    return render_template('daily_management/photo_upload.html',
                          title=f'{school.name} - 上传检查照片',
                          school=school,
                          log_id=log_id)

# 生成二维码的辅助函数
def generate_qrcode_base64(url):
    """生成二维码并返回base64编码的图像

    参数:
        url: 要编码的URL

    返回:
        base64编码的二维码图像
    """
    try:
        import qrcode
        import base64
        from io import BytesIO

        # 创建二维码
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(url)
        qr.make(fit=True)

        # 创建图像
        img = qr.make_image(fill_color="black", back_color="white")

        # 转换为base64
        buffered = BytesIO()
        img.save(buffered)
        img_str = base64.b64encode(buffered.getvalue()).decode()

        return img_str
    except ImportError:
        # 如果没有安装qrcode包，返回一个空字符串
        current_app.logger.error("未安装qrcode包，无法生成二维码")
        return ""

# 生成照片上传二维码（重定向到固定二维码页面）
@daily_management_bp.route('/photo-upload/qrcode/<int:log_id>')
@login_required
def generate_photo_upload_qrcode(log_id):
    """生成照片上传二维码（重定向到固定二维码页面）

    参数:
        log_id: 日志ID（兼容性参数，实际不使用）

    返回:
        重定向到固定二维码页面
    """
    # 重定向到新的固定二维码页面
    return redirect(url_for('daily_management.generate_fixed_inspection_qrcode'))

# 添加检查照片
@daily_management_bp.route('/inspection-photo/add/<int:log_id>', methods=['GET', 'POST'])
@login_required
def add_inspection_photo(log_id):
    """添加检查照片

    参数:
        log_id: 日志ID

    返回:
        GET: 渲染添加检查照片页面
        POST: 处理表单提交，保存检查照片
    """
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法添加检查照片', 'danger')
        return redirect(url_for('main.index'))

    # 获取日志并确保属于当前用户的学校
    log = DailyLog.query.filter_by(id=log_id, area_id=user_area.id).first_or_404()

    # 获取检查类型
    inspection_type = request.args.get('type', 'morning')
    if inspection_type not in ['morning', 'noon', 'evening']:
        inspection_type = 'morning'

    # 创建空表单对象，用于生成 CSRF 令牌
    form = FlaskForm()

    if request.method == 'POST':
        try:
            # 获取表单数据
            inspection_type = request.form.get('inspection_type')
            rating = request.form.get('rating', type=int)
            description = request.form.get('description', '')

            # 检查是否上传了照片
            if 'photos' not in request.files:
                flash('请上传照片', 'danger')
                return redirect(request.url)

            photo_file = request.files['photos']
            if photo_file.filename == '':
                flash('请选择照片文件', 'danger')
                return redirect(request.url)

            # 保存照片
            from app.utils.file_handler import save_photo
            file_path = save_photo(photo_file, f'inspection_{inspection_type}')

            # 创建照片记录
            photo = Photo(
                file_path=file_path,
                reference_type=inspection_type,
                reference_id=log_id,
                rating=rating,
                description=description,
                upload_time=datetime.now()
            )
            db.session.add(photo)
            db.session.commit()

            flash('检查照片添加成功', 'success')
            return redirect(url_for('daily_management.simplified_inspection', log_id=log_id))
        except Exception as e:
            db.session.rollback()
            flash(f'添加检查照片失败: {str(e)}', 'danger')
            return redirect(request.url)

    return render_template('daily_management/add_inspection_photo.html',
                          title='添加检查照片',
                          log=log,
                          inspection_type=inspection_type,
                          form=form)

# 查看检查照片详情
@daily_management_bp.route('/inspection-photo/view/<int:photo_id>')
@login_required
def view_inspection_photo(photo_id):
    """查看检查照片详情

    参数:
        photo_id: 照片ID

    返回:
        渲染检查照片详情页面
    """
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法查看检查照片', 'danger')
        return redirect(url_for('main.index'))

    # 获取照片
    try:
        # 使用原始SQL查询获取照片
        sql = text("""
        SELECT p.id, p.file_path, p.reference_type, p.reference_id, p.rating, p.description, p.upload_time
        FROM photos p
        WHERE p.id = :photo_id
        """)

        result = db.session.execute(sql, {'photo_id': photo_id})
        photo_data = result.fetchone()

        if not photo_data:
            flash('照片不存在', 'danger')
            return redirect(url_for('daily_management.index'))

        # 构建照片对象
        photo = {
            'id': photo_data[0],
            'file_path': photo_data[1],
            'reference_type': photo_data[2],
            'reference_id': photo_data[3],
            'rating': photo_data[4],
            'description': photo_data[5],
            'upload_time': photo_data[6]
        }

        # 获取日志
        log = DailyLog.query.get_or_404(photo['reference_id'])

        # 创建空表单对象，用于生成 CSRF 令牌
        form = FlaskForm()

        return render_template('daily_management/view_inspection_photo.html',
                              title='检查照片详情',
                              photo=photo,
                              log=log,
                              form=form,
                              school=user_area)
    except Exception as e:
        flash(f'获取照片详情失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.index'))

# 编辑检查照片
@daily_management_bp.route('/inspection-photo/edit/<int:photo_id>', methods=['GET', 'POST'])
@login_required
def edit_inspection_photo(photo_id):
    """编辑检查照片

    参数:
        photo_id: 照片ID

    返回:
        GET: 渲染编辑检查照片页面
        POST: 处理表单提交，更新检查照片
    """
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法编辑检查照片', 'danger')
        return redirect(url_for('main.index'))

    # 获取照片
    try:
        # 使用原始SQL查询获取照片
        sql = text("""
        SELECT id, file_path, reference_type, reference_id, rating, description, upload_time
        FROM photos
        WHERE id = :photo_id
        """)

        result = db.session.execute(sql, {'photo_id': photo_id})
        photo_data = result.fetchone()

        if not photo_data:
            flash('照片不存在', 'danger')
            return redirect(url_for('daily_management.index'))

        # 构建照片对象
        photo = {
            'id': photo_data[0],
            'file_path': photo_data[1],
            'reference_type': photo_data[2],
            'reference_id': photo_data[3],
            'rating': photo_data[4],
            'description': photo_data[5],
            'upload_time': photo_data[6]
        }

        # 创建空表单对象，用于生成 CSRF 令牌
        form = FlaskForm()

        if request.method == 'POST':
            try:
                # 获取表单数据
                inspection_type = request.form.get('inspection_type')
                rating = request.form.get('rating', type=int)
                description = request.form.get('description', '')

                # 更新照片信息
                sql = text("""
                UPDATE photos
                SET reference_type = :reference_type, rating = :rating, description = :description
                WHERE id = :photo_id
                """)

                db.session.execute(sql, {
                    'reference_type': inspection_type,
                    'rating': rating,
                    'description': description,
                    'photo_id': photo_id
                })

                # 检查是否上传了新照片
                if 'new_photo' in request.files and request.files['new_photo'].filename != '':
                    # 保存新照片
                    from app.utils.file_handler import save_photo
                    file_path = save_photo(request.files['new_photo'], f'inspection_{inspection_type}')

                    # 更新照片路径
                    sql = text("""
                    UPDATE photos
                    SET file_path = :file_path
                    WHERE id = :photo_id
                    """)

                    db.session.execute(sql, {
                        'file_path': file_path,
                        'photo_id': photo_id
                    })

                db.session.commit()
                flash('检查照片更新成功', 'success')
                return redirect(url_for('daily_management.view_inspection_photo', photo_id=photo_id))
            except Exception as e:
                db.session.rollback()
                flash(f'更新检查照片失败: {str(e)}', 'danger')
                return redirect(request.url)

        return render_template('daily_management/edit_inspection_photo.html',
                              title='编辑检查照片',
                              photo=photo,
                              form=form)
    except Exception as e:
        flash(f'获取照片详情失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.index'))

# 删除检查照片
@daily_management_bp.route('/inspection-photo/delete/<int:photo_id>', methods=['POST'])
@login_required
def delete_inspection_photo(photo_id):
    """删除检查照片

    参数:
        photo_id: 照片ID

    返回:
        重定向到检查记录页面
    """
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法删除检查照片', 'danger')
        return redirect(url_for('main.index'))

    try:
        # 获取照片信息
        sql = text("""
        SELECT reference_id FROM photos WHERE id = :photo_id
        """)
        result = db.session.execute(sql, {'photo_id': photo_id})
        photo_data = result.fetchone()

        if not photo_data:
            flash('照片不存在', 'danger')
            return redirect(url_for('daily_management.index'))

        log_id = photo_data[0]

        # 删除照片
        sql = text("""
        DELETE FROM photos WHERE id = :photo_id
        """)
        db.session.execute(sql, {'photo_id': photo_id})
        db.session.commit()

        flash('检查照片删除成功', 'success')
        return redirect(url_for('daily_management.simplified_inspection', log_id=log_id))
    except Exception as e:
        db.session.rollback()
        flash(f'删除检查照片失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.index'))

# 打印检查照片
@daily_management_bp.route('/inspection-photos/print/<int:log_id>')
@login_required
def print_inspection_photos(log_id):
    """打印检查照片

    参数:
        log_id: 日志ID

    返回:
        渲染打印检查照片页面
    """
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法打印检查照片', 'danger')
        return redirect(url_for('main.index'))

    # 获取日志并确保属于当前用户的学校
    log = DailyLog.query.filter_by(id=log_id, area_id=user_area.id).first_or_404()

    # 获取照片
    try:
        # 使用原始SQL查询获取照片
        morning_photos = []
        noon_photos = []
        evening_photos = []

        # 查询早晨照片
        sql = text("""
        SELECT id, file_path, rating, upload_time, description
        FROM photos
        WHERE reference_type = 'morning' AND reference_id = :reference_id
        ORDER BY upload_time DESC
        """)

        result = db.session.execute(sql, {'reference_id': log_id})

        for row in result:
            morning_photos.append({
                'id': row[0],
                'file_path': row[1],
                'rating': row[2],
                'upload_time': row[3],
                'description': row[4]
            })

        # 查询中午照片
        sql = text("""
        SELECT id, file_path, rating, upload_time, description
        FROM photos
        WHERE reference_type = 'noon' AND reference_id = :reference_id
        ORDER BY upload_time DESC
        """)

        result = db.session.execute(sql, {'reference_id': log_id})

        for row in result:
            noon_photos.append({
                'id': row[0],
                'file_path': row[1],
                'rating': row[2],
                'upload_time': row[3],
                'description': row[4]
            })

        # 查询晚上照片
        sql = text("""
        SELECT id, file_path, rating, upload_time, description
        FROM photos
        WHERE reference_type = 'evening' AND reference_id = :reference_id
        ORDER BY upload_time DESC
        """)

        result = db.session.execute(sql, {'reference_id': log_id})

        for row in result:
            evening_photos.append({
                'id': row[0],
                'file_path': row[1],
                'rating': row[2],
                'upload_time': row[3],
                'description': row[4]
            })

        return render_template('daily_management/print_inspection_photos.html',
                              title='打印检查照片',
                              log=log,
                              morning_photos=morning_photos,
                              noon_photos=noon_photos,
                              evening_photos=evening_photos,
                              now=datetime.now(),
                              school=user_area)
    except Exception as e:
        flash(f'获取照片失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.simplified_inspection', log_id=log_id))

# 打印单张检查照片详情
@daily_management_bp.route('/inspection-photo/print/<int:photo_id>')
@login_required
def print_inspection_photo_detail(photo_id):
    """打印单张检查照片详情

    参数:
        photo_id: 照片ID

    返回:
        渲染打印单张检查照片详情页面
    """
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法打印检查照片', 'danger')
        return redirect(url_for('main.index'))

    # 获取照片
    try:
        # 使用原始SQL查询获取照片
        sql = text("""
        SELECT p.id, p.file_path, p.reference_type, p.reference_id, p.rating, p.description, p.upload_time
        FROM photos p
        WHERE p.id = :photo_id
        """)

        result = db.session.execute(sql, {'photo_id': photo_id})
        photo_data = result.fetchone()

        if not photo_data:
            flash('照片不存在', 'danger')
            return redirect(url_for('daily_management.index'))

        # 构建照片对象
        photo = {
            'id': photo_data[0],
            'file_path': photo_data[1],
            'reference_type': photo_data[2],
            'reference_id': photo_data[3],
            'rating': photo_data[4],
            'description': photo_data[5],
            'upload_time': photo_data[6]
        }

        # 获取日志
        log = DailyLog.query.get_or_404(photo['reference_id'])

        return render_template('daily_management/print_inspection_photo_detail.html',
                              title='打印检查照片详情',
                              photo=photo,
                              log=log,
                              now=datetime.now(),
                              school=user_area)
    except Exception as e:
        flash(f'获取照片详情失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.index'))

# 评价检查照片
@daily_management_bp.route('/rate-inspection-photos/<int:log_id>', methods=['GET'])
@login_required
def rate_inspection_photos(log_id):
    """评价检查照片页面

    参数:
        log_id: 日志ID

    返回:
        渲染评价检查照片页面
    """
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法评价检查照片', 'danger')
        return redirect(url_for('main.index'))

    # 获取日志并确保属于当前用户的学校
    log = DailyLog.query.filter_by(id=log_id, area_id=user_area.id).first_or_404()

    # 获取所有照片
    photos = Photo.query.filter_by(reference_id=log_id).filter(
        Photo.reference_type.in_(['morning', 'noon', 'evening'])
    ).order_by(Photo.upload_time.desc()).all()

    # 按时段分类照片
    morning_photos = [p for p in photos if p.reference_type == 'morning']
    noon_photos = [p for p in photos if p.reference_type == 'noon']
    evening_photos = [p for p in photos if p.reference_type == 'evening']

    return render_template('daily_management/rate_inspection_photos.html',
                          title=f'{user_area.name} - 评价检查照片',
                          log=log,
                          photos=photos,
                          morning_photos=morning_photos,
                          noon_photos=noon_photos,
                          evening_photos=evening_photos,
                          school=user_area)

# 简化检查记录页面
@daily_management_bp.route('/simplified-inspection/<int:log_id>', methods=['GET'])
@login_required
def simplified_inspection(log_id):
    """简化检查记录页面，只按时段分类，重点关注照片

    参数:
        log_id: 日志ID

    返回:
        渲染后的简化检查记录页面
    """
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法查看检查记录', 'danger')
        return redirect(url_for('main.index'))

    # 获取日志并确保属于当前用户的学校
    log = DailyLog.query.filter_by(id=log_id, area_id=user_area.id).first_or_404()

    # 获取前一天和后一天的日志
    from app.utils.datetime_helper import get_prev_next_logs
    prev_log, next_log = get_prev_next_logs(log)

    # 获取照片
    try:
        # 使用原始SQL查询获取照片
        morning_photos = []
        noon_photos = []
        evening_photos = []

        # 查询早晨照片
        sql = text("""
        SELECT id, file_path, rating, upload_time
        FROM photos
        WHERE reference_type = 'morning' AND reference_id = :reference_id
        ORDER BY upload_time DESC
        """)

        result = db.session.execute(sql, {'reference_id': log_id})

        for row in result:
            morning_photos.append({
                'id': row[0],
                'file_path': row[1],
                'rating': row[2],
                'upload_time': row[3]
            })

        # 查询中午照片
        sql = text("""
        SELECT id, file_path, rating, upload_time
        FROM photos
        WHERE reference_type = 'noon' AND reference_id = :reference_id
        ORDER BY upload_time DESC
        """)

        result = db.session.execute(sql, {'reference_id': log_id})

        for row in result:
            noon_photos.append({
                'id': row[0],
                'file_path': row[1],
                'rating': row[2],
                'upload_time': row[3]
            })

        # 查询晚上照片
        sql = text("""
        SELECT id, file_path, rating, upload_time
        FROM photos
        WHERE reference_type = 'evening' AND reference_id = :reference_id
        ORDER BY upload_time DESC
        """)

        result = db.session.execute(sql, {'reference_id': log_id})

        for row in result:
            evening_photos.append({
                'id': row[0],
                'file_path': row[1],
                'rating': row[2],
                'upload_time': row[3]
            })
    except Exception as e:
        # 如果photos表不存在，忽略错误
        current_app.logger.error(f"获取照片失败: {str(e)}")
        morning_photos = []
        noon_photos = []
        evening_photos = []

    # 获取今天的日期，用于日期导航
    today = date.today()
    today_str = today.strftime('%Y-%m-%d')

    # 导入 timedelta，用于日期计算
    from datetime import timedelta

    # 创建空表单对象，用于生成 CSRF 令牌
    form = FlaskForm()

    return render_template('daily_management/simplified_inspection.html',
                          title=f'{user_area.name} - 检查记录',
                          log=log,
                          prev_log=prev_log,
                          next_log=next_log,
                          morning_photos=morning_photos,
                          noon_photos=noon_photos,
                          evening_photos=evening_photos,
                          today=today,
                          today_str=today_str,
                          timedelta=timedelta,
                          form=form,
                          school=user_area)
