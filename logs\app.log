2025-05-31 22:57:11,337 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 22:57:12,726 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 23:06:12,605 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-05-31 23:06:12,607 ERROR: 获取最近陪餐记录失败: name 'db' is not defined [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:89]
2025-05-31 23:06:34,259 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\models_visibility.py:54]
2025-05-31 23:06:34,356 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-05-31 23:06:34,356 ERROR: 获取最近陪餐记录失败: name 'db' is not defined [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:89]
2025-05-31 23:07:11,884 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-05-31 23:07:11,887 ERROR: 获取最近陪餐记录失败: name 'db' is not defined [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:89]
2025-05-31 23:09:56,180 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 23:10:28,451 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 23:10:58,516 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 23:11:30,902 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 23:11:41,315 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 23:11:46,490 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-05-31 23:11:46,493 ERROR: 获取最近陪餐记录失败: name 'db' is not defined [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:89]
2025-05-31 23:15:02,878 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 23:15:17,833 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 23:15:37,286 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 23:15:52,130 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 23:16:51,354 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 23:16:52,747 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 23:18:56,871 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-05-31 23:18:56,875 ERROR: 获取最近陪餐记录失败: name 'db' is not defined [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:89]
2025-05-31 23:22:06,080 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-05-31 23:22:06,082 ERROR: 获取最近陪餐记录失败: name 'db' is not defined [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:89]
2025-05-31 23:23:33,892 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 23:23:55,920 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 23:23:59,806 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-05-31 23:23:59,807 ERROR: 获取最近陪餐记录失败: name 'db' is not defined [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:89]
2025-05-31 23:24:22,525 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 23:24:25,601 WARNING: 无法解析日期时间字符串:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\datetime_helper.py:297]
2025-05-31 23:24:42,400 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 23:25:22,588 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 23:25:52,941 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 23:25:54,167 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:775]
2025-05-31 23:26:05,279 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-05-31 23:26:05,280 ERROR: 获取最近陪餐记录失败: name 'db' is not defined [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:89]
2025-05-31 23:26:09,254 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-05-31 23:26:09,256 ERROR: 获取最近陪餐记录失败: name 'db' is not defined [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:89]
2025-05-31 23:26:36,175 WARNING: 无法解析日期时间字符串:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\datetime_helper.py:297]
2025-05-31 23:28:14,531 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-05-31 23:28:14,531 ERROR: 获取最近陪餐记录失败: name 'db' is not defined [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:89]
2025-05-31 23:28:30,056 WARNING: 无法解析日期时间字符串:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\datetime_helper.py:297]
2025-05-31 23:33:47,366 WARNING: 无法解析日期时间字符串:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\datetime_helper.py:297]
2025-05-31 23:34:00,499 INFO: 自动填充菜单数据: 早餐=红椒培根汤、健康黄瓜汤、蔬菜肉丸汤, 午餐=香甜牛排汤、鸡肉焖、香肠焖, 晚餐=猪肉饭、鸡翅拌 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\daily_management_service.py:299]
2025-05-31 23:34:00,643 WARNING: 无法解析日期时间字符串:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\datetime_helper.py:297]
2025-05-31 23:34:03,175 WARNING: 无法解析日期时间字符串:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\datetime_helper.py:297]
2025-05-31 23:34:04,738 WARNING: 无法解析日期时间字符串:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\datetime_helper.py:297]
2025-05-31 23:34:06,858 WARNING: 无法解析日期时间字符串:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\datetime_helper.py:297]
2025-05-31 23:34:08,291 WARNING: 无法解析日期时间字符串:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\datetime_helper.py:297]
2025-05-31 23:34:14,429 WARNING: 无法解析日期时间字符串:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\datetime_helper.py:297]
2025-05-31 23:34:20,034 WARNING: 无法解析日期时间字符串:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\datetime_helper.py:297]
2025-05-31 23:35:59,719 INFO: 获取周菜单: area_id=42, week_start=2025-05-26, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-05-31 23:35:59,719 INFO: 使用日期字符串: 2025-05-26 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-05-31 23:35:59,719 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-05-31 23:35:59,719 INFO: SQL参数: area_id=42, week_start_str=2025-05-26 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
2025-05-31 23:35:59,721 INFO: SQL查询成功，找到菜单: id=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:70]
2025-05-31 23:35:59,723 INFO: 通过ID获取完整菜单对象成功: id=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]
